from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from services.store_service import StoreService
from schema.store_schema import (
    DeleteRequest,
    FindSearchParams,
    FindStoreResponse,
    StoreAttributionResponse,
    StoreFilterValuesResponse,
    StoreListTotalResponse,
    StoreListResponse,
    StoreSearchRequest,
    StoreSearchResponse,
    NutroStoreDetailResponse,
    NutroStoreMetaDataResponse,
    ChangeLogResponse,
    MappingDetailResponse,
    StoreSuggestionResponse,
    StoreUpdateRequest,
    UnmappedStoreSearchResponse,
    DistStoreSearchResponse,
    DistStoreSearchRequest,
    UnmappedStoreSearchRequest,
    StoreCreateRequest,
    StoreCreateResponse,
    MappingUnmappingMetadataRequest,
    MappingUnmappingUpdateMetadataRequest,
    MappingUnmappingUpdateMetadataResponse,
)
from api.v1.deps import get_db, Transaction
from api.v1.wrappers import retry_request

router = APIRouter()

store_service = StoreService()


@router.get("/search", response_model=StoreSearchResponse)
@retry_request(max_retries=5, delay=5)
def search_stores(
    NutroStoreID: Optional[str] = Query(
        default=None, max_length=50, description="Nutro Store ID"
    ),
    ChainName: Optional[str] = Query(
        default=None, max_length=50, description="Chain Name"
    ),
    StoreName: Optional[str] = Query(
        default=None, max_length=50, description="Store Name"
    ),
    StoreNumber: Optional[str] = Query(
        default=None, max_length=50, description="Store Number"
    ),
    Address: Optional[str] = Query(
        default=None, max_length=50, description="Store Address"
    ),
    City: Optional[str] = Query(default=None, max_length=50, description="City"),
    State: Optional[str] = Query(default=None, max_length=50, description="State"),
    ZipCode: Optional[str] = Query(default=None, max_length=50, description="Zip Code"),
    Phone: Optional[str] = Query(
        default=None, max_length=50, description="Phone Number"
    ),
    TerritoryDivID: Optional[str] = Query(
        default=None, max_length=10, description="Territory Division ID"
    ),
    page: int = Query(default=1, ge=1, description="Page number (1-based)"),
    limit: int = Query(
        default=100, ge=1, le=1000, description="Number of records per page"
    ),
    generic_search: Optional[str] = Query(
        default=None, max_length=50, description="Phone Number"
    ),
    db: Session = Depends(get_db),
):
    """
    Search stores using OR-based logic for generic search or AND-based logic for specific fields

    This endpoint provides two search modes:
    1. Generic Search (OR logic): When 'generic_search' is provided, searches across all fields
       sequentially and returns stores matching ANY field
    2. Specific Field Search (AND logic): When individual field parameters are provided,
       uses the traditional AND-based filtering

    All parameters are optional and empty strings are converted to NULL.

    Example usage:
    - GET /api/v1/stores/search?generic_search=PetChain (searches all fields for "PetChain")
    - GET /api/v1/stores/search?NutroStoreID=12345&ChainName=PetChain (AND-based search)
    - GET /api/v1/stores/search?City=New%20York&State=NY&page=2&limit=50

    Returns:
        StoreSearchResponse: Paginated list of stores matching the search criteria
    """
    try:
        # Execute search with transaction management
        with Transaction(db) as session:
            # Use OR-based search for generic search, AND-based for specific fields
            if generic_search:
                # Use new OR-based sequential search method
                return store_service.search_stores_or_logic(
                    session.db, generic_search, page=page, limit=limit
                )
            else:
                # Use existing AND-based search for specific field searches
                search_params = StoreSearchRequest(
                    NutroStoreID=NutroStoreID,
                    ChainName=ChainName,
                    StoreName=StoreName,
                    StoreNumber=StoreNumber,
                    Address=Address,
                    City=City,
                    State=State,
                    ZipCode=ZipCode,
                    Phone=Phone,
                    TerritoryDivID=TerritoryDivID,
                )
                return store_service.search_stores(
                    session.db, search_params, page=page, limit=limit
                )

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500, detail=f"Unexpected error during store search: {str(e)}"
        )
    except Exception:
        # Re-raise original exception
        raise


@router.get("/nutro-detail/{nutrostore_id}", response_model=NutroStoreDetailResponse)
@retry_request(max_retries=5, delay=5)
def get_nutro_store_detail(
    nutrostore_id: int,
    page: Optional[int] = Query(
        default=None, ge=1, description="Page number (1-based) for pagination"
    ),
    limit: Optional[int] = Query(
        default=None, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """
    Get detailed information for a specific nutro store with optional pagination, this is the
    data that gets displayed in tabular representation  when user clicks on store description
    in UI.

    This endpoint calls the [nutrostore].[store_mapping_SelectByNutroStoreID] stored procedure
    to fetch all detailed store mapping records for the specified NutroStoreID.
    Since one NutroStoreID can have multiple mappings/data sources, pagination is supported.

    Args:
        nutrostore_id (int): The nutro store ID to fetch details for
        page (int, optional): Page number for pagination (1-based)
        limit (int, optional): Number of records per page

    Returns:
        NutroStoreDetailResponse: Object containing:
        - stores: List of detailed store information records, each including:
          - DataSource: Data source information
          - DistStoreID: Distribution store ID
          - DistStoreName: Distribution store name/description
          - DistStoreLocation: Combined city, state, zip location
          - LastTransactionDate: Last transaction date in MM/DD/YYYY format
          - L52POSSales: POS sales since 5/2/25
          - L4POSSales: POS sales since 5/31/24
          - TotalPOSSales: Total POS sales since 1/1/10
          - MappingStatus: Current mapping status
        - total_count: Total number of records (before pagination)
        - page: Current page number (if pagination used)
        - limit: Records per page (if pagination used)
        - has_next: Whether there are more pages
        - has_previous: Whether there are previous pages

    Raises:
        HTTPException: 404 if no stores found, 500 for database errors

    Example usage:
        GET /api/v1/stores/nutro-detail/150088
        GET /api/v1/stores/nutro-detail/150088?page=1&limit=50
    """
    try:
        # Execute store detail lookup with transaction management
        with Transaction(db) as session:
            return store_service.get_nutro_store_detail(
                session.db, nutrostore_id, page=page, limit=limit
            )

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error fetching nutro store detail: {str(e)}",
        )
    except Exception:
        # Re-raise original exception
        raise


@router.get(
    "/nutro-metadata/{nutrostore_id}", response_model=NutroStoreMetaDataResponse
)
@retry_request(max_retries=5, delay=5)
def get_nutro_store_metadata(nutrostore_id: int, db: Session = Depends(get_db)):
    """
    Get metadata information for a specific nutro store (all matching records)

    This endpoint calls the [nutrostore].[store_Select] stored procedure
    to fetch all metadata records for the specified NutroStoreID.

    Args:
        nutrostore_id (int): The nutro store ID to fetch metadata for

    Returns:
        NutroStoreMetaDataResponse: Object containing:
        - stores: List of store metadata records, each including:
          - Address: Complete address (LocationAddress, LocationCity, MailingState MailingZipCode)
          - Phone: Store phone number
          - Contact: Store contact person
          - Chain: Chain name
          - AccountType: Account type
          - CategoryLevelOne: Category level one (Account type detail)
          - CategoryLevelTwo: Category level two (Report category)
          - CategoryLevelThree: Category level three (Report category detail)
          - Status: Store status description
          - TM: Territory Manager
        - total_count: Total number of records returned

    Raises:
        HTTPException: 404 if no stores found, 500 for database errors

    Example usage:
        GET /api/v1/stores/nutro-metadata/150088
    """
    try:
        # Execute store metadata lookup with transaction management
        with Transaction(db) as session:
            return store_service.get_nutro_store_metadata(session.db, nutrostore_id)

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error fetching nutro store metadata: {str(e)}",
        )
    except Exception:
        # Re-raise original exception
        raise


@router.get("/change-log", response_model=ChangeLogResponse)
@retry_request(max_retries=5, delay=5)
def get_change_log(
    nutrostore_id: int = Query(description="Nutro Store ID to fetch change logs for"),
    date: Optional[str] = Query(
        default=None,
        description="Optional date filter in mmddyyyy format (e.g., 05272025)",
    ),
    page: Optional[int] = Query(
        default=None, ge=1, description="Page number (1-based) for pagination"
    ),
    limit: Optional[int] = Query(
        default=None, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """
    Get change log entries for a specific nutro store with optional date filtering and pagination

    This endpoint calls the [nutrostore].[store_ChangeLog_Select] stored procedure
    to fetch change log records for the specified NutroStoreID.

    Args:
        nutrostore_id (int): The nutro store ID to fetch change logs for
        date (str, optional): Date filter in mmddyyyy format (e.g., 05272025)
        page (int, optional): Page number for pagination (1-based)
        limit (int, optional): Number of records per page

    Returns:
        ChangeLogResponse: Object containing:
        - changes: List of change log entries, each including:
          - ChangeDate: Change date in MM/DD/YYYY H:MM:SS AM/PM format
          - LastModifiedBy: User who made the change
          - ChangeType: Type of change made
        - total_count: Total number of records (after date filtering)
        - page: Current page number (if pagination used)
        - limit: Records per page (if pagination used)
        - has_next: Whether there are more pages
        - has_previous: Whether there are previous pages

    Raises:
        HTTPException: 400 for invalid date format, 500 for database errors

    Example usage:
        GET /api/v1/stores/change-log?nutrostore_id=150088
        GET /api/v1/stores/change-log?nutrostore_id=150088&date=05272025
        GET /api/v1/stores/change-log?nutrostore_id=150088&date=05272025&page=1&limit=50
    """
    try:
        # Execute change log lookup with transaction management
        with Transaction(db) as session:
            return store_service.get_change_log(
                session.db,
                nutrostore_id=nutrostore_id,
                date_filter=date,
                page=page,
                limit=limit,
            )

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500, detail=f"Unexpected error fetching change log: {str(e)}"
        )
    except Exception:
        # Re-raise original exception
        raise


@router.get("/mapping/details", response_model=MappingDetailResponse)
@retry_request(max_retries=5, delay=5)
def get_mapping_details(
    mapping_id: int = Query(description="Mapping ID (VCID) to fetch details for"),
    nutrostore_id=Query(default=None, description="Nutro Store ID (currently ignored)"),
    db: Session = Depends(get_db),
):
    """
    Get mapping details for a specific mapping ID

    This endpoint calls the [nutrostore].[store_mapping_Select] stored procedure
    to fetch mapping details for the specified mapping_id (VCID).

    Args:
        mapping_id (int): The mapping ID (VCID) to fetch details for
        nutrostore_id (int, optional): The nutro store ID (currently ignored as per requirements)

    Returns:
        MappingDetailResponse: Object containing:
        - mappings: List of mapping detail records, each including:
          - DataSource: Data source information
          - DataStoreName: Data store name
          - DataStoreID: Data store ID
          - Address: Complete address (DistStoreAddress, DistStoreCity, DistStoreState, DistStoreZip)
          - CustomerType: Customer type
          - Warehouse: Warehouse information
          - Rep: Representative
          - Country: Store country
          - FirstTransDate: First transaction date in MM/DD/YYYY format
          - LastTransDate: Last transaction date in MM/DD/YYYY format
          - Last4WeekPOS: Last 4 week POS sales
          - Last52WeekPOS: Last 52 week POS sales
          - Total2010On: Total POS sales from 2010 onwards
          - Phone: Store phone number
          - MappingStatus: Mapping status
          - IsPending: Whether the mapping is pending (boolean)
        - total_count: Total number of records returned

    Raises:
        HTTPException: 500 for database errors

    Example usage:
        GET /api/v1/stores/mapping/details?mapping_id=123&nutrostore_id=456
    """
    try:
        # Execute mapping details lookup with transaction management
        with Transaction(db) as session:
            return store_service.get_mapping_details(
                session.db, mapping_id=mapping_id, nutrostore_id=nutrostore_id
            )

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error fetching mapping details: {str(e)}",
        )
    except Exception:
        # Re-raise original exception
        raise


@router.get(
    "/dist-stores-or-mapped-stores/search", response_model=DistStoreSearchResponse
)
@retry_request(max_retries=5, delay=5)
def search_dist_stores_or_mapped_stores(
    generic_search: Optional[str] = Query(
        default=None,
        max_length=50,
        description="Generic search across all fields (OR logic)",
    ),
    NutroStoreID: Optional[str] = Query(
        default=None, max_length=50, description="Nutro Store ID"
    ),
    DataSource: Optional[str] = Query(
        default=None, max_length=50, description="Data Source"
    ),
    DistStoreID: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store ID"
    ),
    DistStoreName: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store Name"
    ),
    DistStoreAddress: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store Address"
    ),
    DistStoreCity: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store City"
    ),
    DistStoreState: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store State"
    ),
    DistStoreZip: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store Zip"
    ),
    DistStorePhone: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store Phone"
    ),
    page: int = Query(default=1, ge=1, description="Page number (1-based)"),
    limit: int = Query(
        default=100, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """
    Search dist stores using OR-based logic for generic search or AND-based logic for specific fields

    This endpoint provides two search modes:
    1. Generic Search (OR logic): When 'generic_search' is provided, searches across all fields
       sequentially and returns stores matching ANY field
    2. Specific Field Search (AND logic): When individual field parameters are provided,
       uses the traditional AND-based filtering

    All parameters are optional and empty strings are converted to NULL.

    Example usage:
    - GET /api/v1/stores/dist-stores-or-mapped-stores/search?generic_search=PetChain (searches all fields)
    - GET /api/v1/stores/dist-stores-or-mapped-stores/search?DataSource=IRI&DistStoreCity=NewYork (AND-based)

    Returns:
        DistStoreSearchResponse: Paginated list of dist stores matching the search criteria
    """
    try:
        # Execute search with transaction management
        with Transaction(db) as session:
            # Use OR-based search for generic search, AND-based for specific fields
            if generic_search:
                # Use new OR-based sequential search method
                return store_service.search_dist_stores_or_mapped_stores_or_logic(
                    session.db, generic_search, page=page, limit=limit
                )
            else:
                # Use existing AND-based search for specific field searches
                search_params = DistStoreSearchRequest(
                    NutroStoreID=NutroStoreID,
                    DataSource=DataSource,
                    DistStoreID=DistStoreID,
                    DistStoreName=DistStoreName,
                    DistStoreAddress=DistStoreAddress,
                    DistStoreCity=DistStoreCity,
                    DistStoreState=DistStoreState,
                    DistStoreZip=DistStoreZip,
                    DistStorePhone=DistStorePhone,
                )
                return store_service.search_dist_stores_or_mapped_stores(
                    session.db, search_params, page=page, limit=limit
                )

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error during dist store search: {str(e)}",
        )
    except Exception:
        # Re-raise original exception
        raise


@router.get("/unmapped-stores/search", response_model=UnmappedStoreSearchResponse)
@retry_request(max_retries=5, delay=5)
def search_unmapped_stores(
    generic_search: Optional[str] = Query(
        default=None,
        max_length=50,
        description="Generic search across all fields (OR logic)",
    ),
    DataSource: Optional[str] = Query(
        default=None, max_length=50, description="Data Source"
    ),
    DistStoreID: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store ID"
    ),
    DistStoreName: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store Name"
    ),
    DistStoreAddress: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store Address"
    ),
    DistStoreCity: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store City"
    ),
    DistStoreState: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store State"
    ),
    DistStoreZip: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store Zip"
    ),
    DistStorePhone: Optional[str] = Query(
        default=None, max_length=50, description="Dist Store Phone"
    ),
    page: int = Query(default=1, ge=1, description="Page number (1-based)"),
    limit: int = Query(
        default=100, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """
    Search unmapped stores using OR-based logic for generic search or AND-based logic for specific fields

    This endpoint provides two search modes:
    1. Generic Search (OR logic): When 'generic_search' is provided, searches across all fields
       sequentially and returns stores matching ANY field
    2. Specific Field Search (AND logic): When individual field parameters are provided,
       uses the traditional AND-based filtering

    All parameters are optional and empty strings are converted to NULL.

    Example usage:
    - GET /api/v1/stores/unmapped-stores/search?generic_search=PetChain (searches all fields)
    - GET /api/v1/stores/unmapped-stores/search?DataSource=IRI&DistStoreCity=NewYork (AND-based)

    Returns:
        UnmappedStoreSearchResponse: Paginated list of unmapped stores matching the search criteria.
        Each store record includes:
        - DataSource: Data source information
        - StoreID: Store identifier
        - StoreDesc: Store description/name
        - CityStateZip: Combined city, state, and zip location
        - FirstDt: First transaction date (MM/DD/YYYY format)
        - LastDt: Last transaction date (MM/DD/YYYY format)
        - Since51525: Sales data since 5/15/25
        - Since61324: Sales data since 6/13/24
        - Since1110: Sales data since 1/1/10
        - Status: Store status
        - VCID: VCID (Mapping ID) - integer identifier for the store mapping
    """
    try:
        # Execute search with transaction management
        with Transaction(db) as session:
            # Use OR-based search for generic search, AND-based for specific fields
            if generic_search:
                # Use new OR-based sequential search method
                return store_service.search_unmapped_stores_or_logic(
                    session.db, generic_search, page=page, limit=limit
                )
            else:
                # Use existing AND-based search for specific field searches
                search_params = UnmappedStoreSearchRequest(
                    DataSource=DataSource,
                    DistStoreID=DistStoreID,
                    DistStoreName=DistStoreName,
                    DistStoreAddress=DistStoreAddress,
                    DistStoreCity=DistStoreCity,
                    DistStoreState=DistStoreState,
                    DistStoreZip=DistStoreZip,
                    DistStorePhone=DistStorePhone,
                )
                return store_service.search_unmapped_stores(
                    session.db, search_params, page=page, limit=limit
                )

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error during unmapped store search: {str(e)}",
        )
    except Exception:
        # Re-raise original exception
        raise


@router.get("/store-description/suggestions", response_model=StoreSuggestionResponse)
@retry_request(max_retries=5, delay=5)
def get_store_suggestions(
    VCID: int = Query(description="VCID to fetch store suggestions for"),
    page: Optional[int] = Query(
        default=None, ge=1, description="Page number (1-based) for pagination"
    ),
    limit: Optional[int] = Query(
        default=None, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """
    Get store suggestions for a specific VCID with optional pagination

    This endpoint calls the [nutrostore].[store_mapping_StoreSuggestions] stored procedure
    to fetch store suggestion records for the specified VCID.

    Args:
        VCID (int): The VCID to fetch store suggestions for
        page (int, optional): Page number for pagination (1-based)
        limit (int, optional): Number of records per page

    Returns:
        StoreSuggestionResponse: Object containing:
        - suggestions: List of store suggestion records, each including:
          - SCORE: Match score (float)
          - NUTRO ID: Nutro Store ID
          - STORE: Store name
          - ADDRESS: Store address
          - CITY: Store city
          - STATE: Store state
          - ZIP: Store zip code
          - PHONE: Store phone number
          - STATUS: Store status
          - TERRITORY MANAGER: Territory manager
        - total_count: Total number of records returned
        - page: Current page number (if pagination used)
        - limit: Records per page (if pagination used)
        - has_next: Whether there are more pages
        - has_previous: Whether there are previous pages

    Raises:
        HTTPException: 500 for database errors

    Example usage:
        GET /api/v1/stores/store-description/suggestions?VCID=123
        GET /api/v1/stores/store-description/suggestions?VCID=123&page=1&limit=50
    """
    try:
        # Execute store suggestions lookup with transaction management
        with Transaction(db) as session:
            return store_service.get_store_suggestions(
                session.db, vcid=VCID, page=page, limit=limit
            )

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error fetching store suggestions: {str(e)}",
        )
    except Exception:
        # Re-raise original exception
        raise


@router.post("/store-description/add", response_model=StoreCreateResponse)
@retry_request(max_retries=5, delay=5)
def create_store(store_data: StoreCreateRequest, db: Session = Depends(get_db)):
    """
    Create a new store using the store_Insert stored procedure

    This endpoint accepts comprehensive store information and creates a new store record
    in the database using the [nutrostore].[store_Insert] stored procedure. The procedure
    generates a new NutroStoreID which is returned in the response.

    Args:
        store_data (StoreCreateRequest): Complete store information including:
            - Basic info: StoreName, ChainName, StoreNumber
            - Location details: LocationType, AccountType, ReportCategory
            - Address info: LocationAddress, MailingAddress with city/state/zip
            - Contact info: StoreContact, Phone, Fax, EmailAddress, WebAddress
            - Business details: BuyingGroup, Distribution_Notes, StoreNotes
            - Territory management: AccountTM, IsCallTarget, Priority, CallFrequency
            - Demo management: DemoTerritoryDivID, IsDemoTarget, DemoPriority
            - Request tracking: RequestBy, RequestNotes
            - System IDs: VCID, NutroStoreID (optional)

    Returns:
        StoreCreateResponse: Object containing:
            - success: Boolean indicating if store was created successfully
            - message: Success or error message
            - nutro_store_id: Generated NutroStoreID from the stored procedure

    Raises:
        HTTPException:
            - 400 for validation errors (invalid data format, missing required fields)
            - 500 for database errors or unexpected errors

    Example usage:
        POST /api/v1/stores/store-description/add
        Content-Type: application/json

        {
            "StoreName": "Pet Paradise Downtown",
            "ChainName": "Pet Paradise",
            "StoreNumber": "PP001",
            "LocationType": "Retail",
            "AccountType": "Independent",
            "LocationAddress": "123 Main St",
            "LocationCity": "New York",
            "LocationState": "NY",
            "LocationZipCode": "10001",
            "Phone": "************",
            "RequestBy": "John Doe"
        }

    Example response:
        {
            "success": true,
            "message": "Store created successfully",
            "nutro_store_id": 150123
        }
    """
    try:
        # Execute store creation with transaction management
        # Using transaction ensures data consistency and proper rollback on errors
        with Transaction(db) as session:
            return store_service.create_store(session.db, store_data)

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500, detail=f"Unexpected error creating store: {str(e)}"
        )
    except Exception:
        # Re-raise original exception
        raise


@router.put("/mapping_unmapping/nutrostore")
@retry_request(max_retries=5, delay=5)
def update_store_mapping(
    request_body: MappingUnmappingMetadataRequest,
    db: Session = Depends(get_db),
):
    """
    Update store mapping metadata.
    """
    try:
        with Transaction(db) as session:
            return store_service.update_store_mapping(
                session.db, request_body.VCID, request_body.NutrostoreID
            )
    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500, detail=f"Unexpected error updating store mapping: {str(e)}"
        )
    except Exception:
        # Re-raise original exception
        raise


@router.put(
    "/mapping_unmapping/update_metadata",
    response_model=MappingUnmappingUpdateMetadataResponse,
)
@retry_request(max_retries=5, delay=5)
def update_store_mapping_metadata(
    request_body: MappingUnmappingUpdateMetadataRequest,
    db: Session = Depends(get_db),
):
    """
    Update store mapping metadata fields in nutrostore.tsStoreMap table

    This endpoint allows updating one or more metadata fields for a store mapping record
    identified by VCID. Only the fields provided in the request payload will be updated.
    Empty strings are converted to NULL values in the database.

    Args:
        request_body (MappingUnmappingUpdateMetadataRequest): Update request containing:
            - VCID (int): Required VCID to identify the record to update
            - customer_type (str, optional): Customer type information
            - rep (str, optional): Representative information
            - address (str, optional): Store address
            - warehouse (str, optional): Warehouse information
            - country (str, optional): Store country
            - phone (str, optional): Store phone number

    Returns:
        MappingUnmappingUpdateMetadataResponse: Object containing:
            - success: Boolean indicating if update was successful
            - message: Success or error message
            - updated_fields: List of fields that were updated
            - VCID: VCID of the updated record

    Raises:
        HTTPException:
            - 400 for validation errors (invalid VCID, no fields to update)
            - 404 if record with specified VCID is not found
            - 500 for database errors or unexpected errors

    Example usage:
        PUT /api/v1/stores/mapping_unmapping/update_metadata
        Content-Type: application/json

        {
            "VCID": 1232,
            "customer_type": "Retail",
            "rep": "John Smith",
            "address": "123 Main St, New York, NY 10001",
            "warehouse": "East Coast Warehouse",
            "country": "USA",
            "phone": "************"
        }

    Example response:
        {
            "success": true,
            "message": "Store mapping metadata updated successfully",
            "updated_fields": ["customer_type", "rep", "address", "warehouse", "country", "phone"],
            "VCID": 1232
        }
    """
    try:
        # Execute metadata update with transaction management
        # Using transaction ensures data consistency and proper rollback on errors
        with Transaction(db) as session:
            return store_service.update_store_mapping_metadata(session.db, request_body)

    except HTTPException as e:
        # Convert HTTPException to 500 status
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error updating store mapping metadata: {str(e)}",
        )
    except Exception:
        # Re-raise original exception
        raise


@router.get("/store-attribution", response_model=StoreListResponse)
@retry_request(max_retries=5, delay=5)
def get_all_stores(
    page: int = Query(1, gt=0),
    limit: int = Query(100, gt=0, le=1000),
    search_query: Optional[str] = Query(
        None, description="Search across multiple fields"
    ),
    store_ids: Optional[str] = Query(None, description="Comma-separated store IDs"),
    store_names: Optional[str] = Query(None, description="Comma-separated store names"),
    chain_names: Optional[str] = Query(None, description="Comma-separated chain names"),
    territory_managers: Optional[str] = Query(
        None, description="Comma-separated territory managers"
    ),
    district_managers: Optional[str] = Query(
        None, description="Comma-separated district managers"
    ),
    store_numbers: Optional[str] = Query(
        None, description="Comma-separated store numbers"
    ),
    account_types: Optional[str] = Query(
        None, description="Comma-separated account types"
    ),
    report_categories: Optional[str] = Query(
        None, description="Comma-separated report categories"
    ),
    db: Session = Depends(get_db),
):
    """
    Get paginated list of stores with filtering capabilities

    Parameters:
    - All filter parameters accept comma-separated values
    - Returns StoreListResponse with pagination metadata
    """

    # Convert comma-separated strings to lists
    def parse_comma_separated(value: Optional[str], type_func=str) -> Optional[List]:
        if not value:
            return None
        return [type_func(item.strip()) for item in value.split(",") if item.strip()]

    try:
        # Execute store suggestions lookup with transaction management
        with Transaction(db) as session:
            return store_service.get_all_stores(
                session.db,
                page=page,
                limit=limit,
                search_query=search_query,
                store_ids=parse_comma_separated(store_ids, int),
                store_names=parse_comma_separated(store_names),
                chain_names=parse_comma_separated(chain_names),
                territory_managers=parse_comma_separated(territory_managers),
                district_managers=parse_comma_separated(district_managers),
                store_numbers=parse_comma_separated(store_numbers),
                account_types=parse_comma_separated(account_types),
                report_categories=parse_comma_separated(report_categories),
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid parameter format: {str(e)}",
        )


@router.get("/store-attribution/all", response_model=StoreListTotalResponse)
@retry_request(max_retries=5, delay=5)
def get_all_stores_total(
    search_query: Optional[str] = Query(
        None, description="Search across multiple fields"
    ),
    store_ids: Optional[str] = Query(None, description="Comma-separated store IDs"),
    store_names: Optional[str] = Query(None, description="Comma-separated store names"),
    chain_names: Optional[str] = Query(None, description="Comma-separated chain names"),
    territory_managers: Optional[str] = Query(
        None, description="Comma-separated territory managers"
    ),
    district_managers: Optional[str] = Query(
        None, description="Comma-separated district managers"
    ),
    store_numbers: Optional[str] = Query(
        None, description="Comma-separated store numbers"
    ),
    account_types: Optional[str] = Query(
        None, description="Comma-separated account types"
    ),
    report_categories: Optional[str] = Query(
        None, description="Comma-separated report categories"
    ),
    count_only: bool = Query(False, description="Return only count of records"),
    limit: int = Query(None, description="Limit number of records"),
    offset: int = Query(0, description="Offset for pagination"),
    db: Session = Depends(get_db),
):
    """
    Get all list of stores with filtering capabilities
    """

    def parse_comma_separated(value: Optional[str], type_func=str) -> Optional[List]:
        if not value:
            return None
        return [type_func(item.strip()) for item in value.split(",") if item.strip()]

    try:
        with Transaction(db) as session:
            if count_only:
                # Optimized count query
                count = store_service.get_stores_count(
                    session.db,
                    search_query=search_query,
                    store_ids=parse_comma_separated(store_ids, int),
                    store_names=parse_comma_separated(store_names),
                    chain_names=parse_comma_separated(chain_names),
                    territory_managers=parse_comma_separated(territory_managers),
                    district_managers=parse_comma_separated(district_managers),
                    store_numbers=parse_comma_separated(store_numbers),
                    account_types=parse_comma_separated(account_types),
                    report_categories=parse_comma_separated(report_categories),
                )
                return StoreListTotalResponse(stores=[], total_count=count)

            return store_service.get_all_stores_total(
                session.db,
                search_query=search_query,
                store_ids=parse_comma_separated(store_ids, int),
                store_names=parse_comma_separated(store_names),
                chain_names=parse_comma_separated(chain_names),
                territory_managers=parse_comma_separated(territory_managers),
                district_managers=parse_comma_separated(district_managers),
                store_numbers=parse_comma_separated(store_numbers),
                account_types=parse_comma_separated(account_types),
                report_categories=parse_comma_separated(report_categories),
                limit=limit,
                offset=offset,
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid parameter format: {str(e)}",
        )


@router.get(
    "/store-attribution/filter-values", response_model=StoreFilterValuesResponse
)
async def get_store_filter_values(
    field: str,
    search: Optional[str] = None,
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_db),
):
    """
    Get paginated filter values for a specific field with search capability
    """
    try:
        print("byeeeeeeeeee")
        # Validate the field name
        valid_fields = [
            "store_ids",
            "store_names",
            "chain_names",
            "territory_managers",
            "district_managers",
            "store_numbers",
            "account_types",
            "report_categories",
        ]

        if field not in valid_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid field name. Must be one of: {', '.join(valid_fields)}",
            )
        # Execute store suggestions lookup with transaction management
        with Transaction(db) as session:
            result = await store_service.fetch_store_filter_values(
                session.db, field, search, limit, offset
            )
            print("heyyyyyyyyyyyyyyyyyyyyyyyyyyy- res", result)
            return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching filter values: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error processing filter values")


@router.put(
    "/store-attribution/{nutro_store_id}", response_model=StoreAttributionResponse
)
@retry_request(max_retries=5, delay=5)
def update_store(
    nutro_store_id: int, update_data: StoreUpdateRequest, db: Session = Depends(get_db)
):
    """
    Update store record by NutroStoreID

    Parameters:
    - nutro_store_id: ID of the store to update
    - update_data: Fields to update (only include fields that should change)

    Returns the updated store record
    """
    return store_service.update_store(db, nutro_store_id, update_data)


@router.delete("/store-attribution/", response_model=dict)
@retry_request(max_retries=5, delay=5)
def delete_stores(delete_request: DeleteRequest, db: Session = Depends(get_db)):
    """
    Delete multiple stores by NutroStoreID

    Parameters:
    - nutro_store_ids: List of NutroStoreIDs to delete

    Returns summary of deletion operation including:
    - deleted_count: Number of successfully deleted records
    - not_found_ids: List of IDs that weren't found
    """
    return store_service.delete_stores(db, delete_request)


@router.get("/find-store", response_model=FindStoreResponse)
def find_store(
    params: FindSearchParams = Depends(),
    page: Optional[int] = Query(
        default=None, ge=1, description="Page number (1-based) for pagination"
    ),
    limit: Optional[int] = Query(
        default=None, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """
    Search for stores using various filters with optional pagination

    This endpoint calls the [nutrostore].[store_mapping_StoreSearch] stored procedure
    to search for stores based on the provided filters.

    Args:
        nutro_store_id: Optional Nutro Store ID filter
        chain_name: Optional Chain name filter
        store_name: Optional Store name filter
        store_number: Optional Store number filter
        address: Optional Address filter
        city: Optional City filter
        state: Optional State filter
        zip_code: Optional Zip code filter
        phone: Optional Phone filter
        page: Optional page number (1-based)
        limit: Optional number of records per page

    Returns:
        StoreSearchResponse: Object containing:
        - results: List of store records, each including:
          - NutroStoreID: Nutro Store ID
          - ChainName: Chain name
          - StoreName: Store name
          - StoreNumber: Store number
          - Address: Store address
          - City: Store city
          - State: Store state
          - ZipCode: Store zip code
          - Phone: Store phone number
        - total_count: Total number of records returned
        - page: Current page number (if pagination used)
        - limit: Records per page (if pagination used)
        - has_next: Whether there are more pages
        - has_previous: Whether there are previous pages

    Raises:
        HTTPException: 500 for database errors

    Example usage:
        GET /api/v1/stores/search?nutro_store_id=150088
        GET /api/v1/stores/search?city=New York&state=NY&page=1&limit=50
    """
    try:
        # Execute store search with transaction management
        with Transaction(db) as session:
            return store_service.find_stores(
                session.db,
                nutro_store_id=params.nutro_store_id,
                chain_name=params.chain_name,
                store_name=params.store_name,
                store_number=params.store_number,
                address=params.address,
                city=params.city,
                state=params.state,
                zip_code=params.zip_code,
                phone=params.phone,
                page=page,
                limit=limit,
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error searching stores: {str(e)}",
        )
