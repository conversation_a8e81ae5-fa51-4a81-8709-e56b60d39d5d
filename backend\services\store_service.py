from concurrent.futures import <PERSON>hr<PERSON><PERSON>ool<PERSON>xecutor
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException, status
from schema.store_schema import (
    DeleteRequest,
    FindStoreResponse,
    StoreListTotalResponse,
    StoreAttributionResponse,
    StoreSearchRequest,
    StoreResponse,
    StoreSearchResponse,
    NutroStoreDetailResponse,
    NutroStoreDetail,
    NutroStoreMetaDataResponse,
    NutroStoreMetaData,
    ChangeLogResponse,
    ChangeLogEntry,
    MappingDetailResponse,
    MappingDetail,
    StoreSuggestionResponse,
    StoreUpdateRequest,
    UnmappedStoreResponse,
    UnmappedStoreSearchRequest,
    UnmappedStoreSearchResponse,
    DistStoreSearchRequest,
    DistStoreResponse,
    DistStoreSearchResponse,
    StoreCreateRequest,
    StoreCreateResponse,
    MappingUnmappingUpdateMetadataRequest,
    MappingUnmappingUpdateMetadataResponse,
    StoreListResponse,
)
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class StoreService:
    """Service class for store-related operations"""

    def search_stores(
        self,
        db: Session,
        search_params: StoreSearchRequest,
        page: int = 1,
        limit: int = 100,
    ) -> StoreSearchResponse:
        """
        Search stores using the store_Search stored procedure with pagination

        Args:
            db: Database session
            search_params: Search parameters
            page: Page number (1-based)
            limit: Number of records per page

        Returns:
            StoreSearchResponse: Paginated search results
        """
        try:
            # Convert empty strings to None for NULL parameters
            params = self._prepare_search_params(search_params)

            # Calculate offset for pagination
            offset = (page - 1) * limit

            # Execute stored procedure with pagination
            stores = self._execute_store_search(db, params, offset, limit)

            # Get total count for pagination metadata
            total_count = self._get_total_count(db, params)

            # Convert results to response objects
            store_responses = [
                StoreResponse(**self._row_to_dict(row)) for row in stores
            ]

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return StoreSearchResponse(
                stores=store_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching stores: {str(e)}",
            )

    def search_stores_or_logic(
        self,
        db: Session,
        generic_search: str,
        page: int = 1,
        limit: int = 100,
    ) -> StoreSearchResponse:
        """
        Search stores using OR logic with sequential field-specific searches

        This method performs sequential searches across all store fields and combines
        the results, providing OR functionality instead of AND filtering.

        Args:
            db: Database session
            generic_search: Search term to apply across all fields
            page: Page number (1-based)
            limit: Number of records per page

        Returns:
            StoreSearchResponse: Paginated search results with deduplicated stores
        """
        try:
            # Define all searchable fields
            search_fields = [
                "NutroStoreID",
                "ChainName",
                "StoreName",
                "StoreNumber",
                "Address",
                "City",
                "State",
                "ZipCode",
                "Phone",
                "TerritoryDivID",
            ]

            # Execute sequential searches for each field
            all_results = self._execute_sequential_field_searches(
                db, generic_search, search_fields
            )

            # Combine and deduplicate results
            combined_stores = self._deduplicate_store_results(all_results)

            # Apply pagination to combined results
            total_count = len(combined_stores)
            offset = (page - 1) * limit
            paginated_stores = combined_stores[offset : offset + limit]

            # Convert to response objects
            store_responses = [
                StoreResponse(**self._row_to_dict(row)) for row in paginated_stores
            ]

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return StoreSearchResponse(
                stores=store_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except Exception as e:
            logger.error(f"Error in OR-based store search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching stores with OR logic: {str(e)}",
            )

    def _prepare_search_params(
        self, search_params: StoreSearchRequest
    ) -> Dict[str, Any]:
        """
        Convert search parameters to dictionary and handle empty strings

        Args:
            search_params: Search parameters from request

        Returns:
            Dict with None values for empty strings
        """
        params = search_params.model_dump()

        # Convert empty strings to None for NULL parameters
        for key, value in params.items():
            if value == "":
                params[key] = None

        return params

    def _execute_store_search(
        self, db: Session, params: Dict[str, Any], offset: int, limit: int
    ) -> List:
        """
        Execute the store_Search stored procedure with pagination

        Args:
            db: Database session
            params: Search parameters
            offset: Number of records to skip
            limit: Number of records to return

        Returns:
            List of result rows
        """
        # Build the stored procedure call
        # Note: We'll handle pagination in Python since stored procedure doesn't support it directly
        sql = text("""
            EXEC [nutrostore].[store_Search]
                @NutroStoreID = :NutroStoreID,
                @ChainName = :ChainName,
                @StoreName = :StoreName,
                @StoreNumber = :StoreNumber,
                @Address = :Address,
                @City = :City,
                @State = :State,
                @ZipCode = :ZipCode,
                @Phone = :Phone,
                @TerritoryDivID = :TerritoryDivID
        """)

        try:
            logger.info(f"Executing store_Search with params: {params}")
            result = db.execute(sql, params)
            all_rows = result.fetchall()

            # Apply pagination in Python
            return all_rows[offset : offset + limit]
        except SQLAlchemyError as e:
            logger.error(f"Database error executing store_Search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error executing stored procedure: {str(e)}",
            )
        except Exception as e:
            logger.error(f"Unexpected error executing store_Search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error executing stored procedure: {str(e)}",
            )

    def _get_total_count(self, db: Session, params: Dict[str, Any]) -> int:
        """
        Get total count of records matching search criteria

        Args:
            db: Database session
            params: Search parameters

        Returns:
            Total count of matching records
        """
        # Execute the stored procedure and count all results
        # This is simpler and more reliable than trying to wrap it in a COUNT query
        sql = text("""
            EXEC [nutrostore].[store_Search]
                @NutroStoreID = :NutroStoreID,
                @ChainName = :ChainName,
                @StoreName = :StoreName,
                @StoreNumber = :StoreNumber,
                @Address = :Address,
                @City = :City,
                @State = :State,
                @ZipCode = :ZipCode,
                @Phone = :Phone,
                @TerritoryDivID = :TerritoryDivID
        """)

        try:
            result = db.execute(sql, params)
            return len(result.fetchall())
        except SQLAlchemyError as e:
            logger.error(f"Database error getting count for store_Search: {str(e)}")
            # Return 0 for count errors to prevent breaking pagination
            return 0
        except Exception as e:
            logger.error(f"Unexpected error getting count for store_Search: {str(e)}")
            return 0

    def _row_to_dict(self, row) -> Dict[str, Any]:
        """
        Convert database row to dictionary

        Args:
            row: Database result row

        Returns:
            Dictionary representation of the row
        """
        # breakpoint()
        return {
            "NutroStoreID": getattr(row, "NutroStoreID", None),
            "ChainName": getattr(row, "ChainName", None),
            "StoreName": getattr(row, "StoreName", None),
            "Address": getattr(row, "Address", None),
            "City": getattr(row, "City", None),
            "State": getattr(row, "State", None),
            "ZipCode": getattr(row, "ZipCode", None),
            "Phone": getattr(row, "Phone", None),
            "TerritoryManager": getattr(
                row, "TerritoryManager", None
            ),  # NEW: TerritoryManager → TerritoryManager
            "Status": getattr(
                row, "StoreStatusDesc", None
            ),  # NEW: StoreStatusDesc → Status
        }

    def _execute_sequential_field_searches(
        self, db: Session, generic_search: str, search_fields: List[str]
    ) -> List:
        """
        Execute sequential searches for each field using a simple for loop

        Args:
            db: Database session
            generic_search: Search term to apply to each field
            search_fields: List of field names to search

        Returns:
            List of all result rows from each field search
        """
        all_results = []

        # Execute searches one by one for each field
        for field_name in search_fields:
            try:
                field_results = self._execute_single_field_search(
                    db, generic_search, field_name
                )
                all_results.extend(field_results)
                logger.info(
                    f"Search for field {field_name} returned {len(field_results)} results"
                )
            except Exception as e:
                logger.warning(f"Search failed for field {field_name}: {str(e)}")
                # Continue with other fields even if one fails
                continue

        return all_results

    def _execute_single_field_search(
        self, db: Session, search_term: str, field_name: str
    ) -> List:
        """
        Execute search for a single field

        Args:
            db: Database session
            search_term: Term to search for
            field_name: Name of the field to search in

        Returns:
            List of result rows from the search
        """
        try:
            # Create search parameters with only the specified field populated
            params = {
                "NutroStoreID": None,
                "ChainName": None,
                "StoreName": None,
                "StoreNumber": None,
                "Address": None,
                "City": None,
                "State": None,
                "ZipCode": None,
                "Phone": None,
                "TerritoryDivID": None,
            }

            # Set the specific field to the search term
            params[field_name] = search_term

            # Execute the stored procedure
            sql = text("""
                EXEC [nutrostore].[store_Search]
                    @NutroStoreID = :NutroStoreID,
                    @ChainName = :ChainName,
                    @StoreName = :StoreName,
                    @StoreNumber = :StoreNumber,
                    @Address = :Address,
                    @City = :City,
                    @State = :State,
                    @ZipCode = :ZipCode,
                    @Phone = :Phone,
                    @TerritoryDivID = :TerritoryDivID
            """)

            result = db.execute(sql, params)
            return result.fetchall()

        except Exception as e:
            logger.error(f"Error searching field {field_name}: {str(e)}")
            return []

    def _deduplicate_store_results(self, all_results: List) -> List:
        """
        Remove duplicate stores from combined results based on NutroStoreID

        Args:
            all_results: Combined list of all search results

        Returns:
            List of unique store records
        """
        seen_ids = set()
        unique_stores = []

        for row in all_results:
            nutro_store_id = getattr(row, "NutroStoreID", None)

            # Skip rows without NutroStoreID or duplicates
            if nutro_store_id is None or nutro_store_id in seen_ids:
                continue

            seen_ids.add(nutro_store_id)
            unique_stores.append(row)

        return unique_stores

    def get_nutro_store_detail(
        self, db: Session, nutrostore_id: int, page: int = None, limit: int = None
    ) -> NutroStoreDetailResponse:
        """
        Get detailed information for a specific nutro store with optional pagination

        Args:
            db: Database session
            nutrostore_id: The nutro store ID to fetch details for
            page: Optional page number (1-based) for pagination
            limit: Optional number of records per page

        Returns:
            NutroStoreDetailResponse: Store detail information with pagination metadata

        Raises:
            HTTPException: 404 if store not found, 500 for other errors
        """
        try:
            logger.info(f"Fetching nutro store detail for ID: {nutrostore_id}")

            # Execute the stored procedure to get store mapping details
            sql = text("""
                EXEC [nutrostore].[store_mapping_SelectByNutroStoreID]
                    @NutroStoreID = :nutrostore_id
            """)

            result = db.execute(sql, {"nutrostore_id": nutrostore_id})
            rows = result.fetchall()

            # Check if any records were returned
            if not rows:
                logger.warning(f"No store found for NutroStoreID: {nutrostore_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Store with NutroStoreID {nutrostore_id} not found",
                )

            # Transform all database rows to response format
            logger.info(f"Found {len(rows)} record(s) for NutroStoreID {nutrostore_id}")
            all_store_details = [
                self._transform_nutro_store_detail(row) for row in rows
            ]

            # Apply pagination if specified
            paginated_details, pagination_meta = self._apply_pagination(
                all_store_details, page, limit
            )

            # Return wrapped response with pagination metadata
            return NutroStoreDetailResponse(
                stores=paginated_details,
                total_count=len(all_store_details),
                page=pagination_meta.get("page"),
                limit=pagination_meta.get("limit"),
                has_next=pagination_meta.get("has_next", False),
                has_previous=pagination_meta.get("has_previous", False),
            )

        except HTTPException as e:
            # Convert HTTPException to 500 status
            logger.error(f"HTTPException fetching nutro store detail: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error fetching store detail: {str(e)}",
            )
        except SQLAlchemyError as e:
            # Convert SQLAlchemyError to descriptive HTTPException
            logger.error(f"Database error fetching nutro store detail: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error fetching store detail: {str(e)}",
            )
        except Exception:
            # Re-raise other exceptions
            raise

    def _transform_nutro_store_detail(self, row) -> NutroStoreDetail:
        """
        Transform database row to NutroStoreDetail format

        Args:
            row: Database result row from stored procedure

        Returns:
            NutroStoreDetail: Transformed individual store detail object
        """
        try:
            # Combine city, state, zip into a single location string
            # Handle None values gracefully
            city = getattr(row, "DistStoreCity", None) or ""
            state = getattr(row, "DistStoreState", None) or ""
            zip_code = getattr(row, "DistStoreZip", None) or ""

            # Format location as "City, State Zip" but handle missing components
            location_parts = []
            if city.strip():
                location_parts.append(city.strip())
            if state.strip():
                location_parts.append(state.strip())
            if zip_code.strip():
                # Add zip to the last part (state) if state exists, otherwise as separate part
                if location_parts:
                    location_parts[-1] = f"{location_parts[-1]} {zip_code.strip()}"
                else:
                    location_parts.append(zip_code.strip())

            # Join with comma and space, or empty string if no parts
            dist_store_location = ", ".join(location_parts) if location_parts else None

            # Format LastTransactionDate to MM/DD/YYYY if it exists
            last_transaction_date = None
            last_dt = getattr(row, "LastTransactionDate", None)
            if last_dt:
                try:
                    # Convert to MM/DD/YYYY format
                    if hasattr(last_dt, "strftime"):
                        last_transaction_date = last_dt.strftime("%m/%d/%Y")
                    else:
                        # Handle string dates if needed
                        last_transaction_date = str(last_dt)
                except Exception as e:
                    logger.warning(f"Error formatting date {last_dt}: {str(e)}")
                    last_transaction_date = str(last_dt)

            # Create and return the individual store detail object with mapped fields
            return NutroStoreDetail(
                DataSource=getattr(row, "DataSource", None),
                DistStoreID=getattr(row, "DistStoreID", None),  # Store ID → DistStoreID
                DistStoreDiscID=getattr(row, "VCID", None),  # Store ID → DistStoreID
                DistStoreName=getattr(
                    row, "DistStoreName", None
                ),  # Dist Store Desc → DistStoreName
                DistStoreLocation=dist_store_location,  # Combined city, state, zip
                LastTransactionDate=last_transaction_date,  # Last Dt → MM/DD/YYYY format
                L52POSSales=getattr(
                    row, "L52POSSales", None
                ),  # Since 5/2/25 assumption
                L4POSSales=getattr(row, "L4POSSales", None),  # Since 5/31/24 assumption
                TotalPOSSales=getattr(row, "TotalPOSSales", None),  # Since 1/1/10
                MappingStatus=getattr(
                    row, "MappingStatus", None
                ),  # Status → MappingStatus
                VCID=getattr(row, "VCID", None),
            )

        except Exception as e:
            logger.error(f"Error transforming nutro store detail: {str(e)}")
            # Return an individual store detail with None values if transformation fails
            return NutroStoreDetail()

    def get_nutro_store_metadata(
        self, db: Session, nutrostore_id: int
    ) -> NutroStoreMetaDataResponse:
        """
        Get metadata information for a specific nutro store using stored procedure

        Args:
            db: Database session
            nutrostore_id: The nutro store ID to fetch metadata for

        Returns:
            NutroStoreMetaDataResponse: Store metadata information

        Raises:
            HTTPException: 404 if store not found, 500 for other errors
        """
        try:
            logger.info(f"Fetching nutro store metadata for ID: {nutrostore_id}")

            # Execute the stored procedure to get store metadata
            sql = text("""
                EXEC [nutrostore].[store_Select]
                    @NutroStoreID = :nutrostore_id
            """)

            result = db.execute(sql, {"nutrostore_id": nutrostore_id})
            rows = result.fetchall()

            # Check if any records were returned
            if not rows:
                logger.warning(
                    f"No store metadata found for NutroStoreID: {nutrostore_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Store metadata with NutroStoreID {nutrostore_id} not found",
                )

            # Transform all database rows to response format
            logger.info(f"Found {len(rows)} metadata record(s), returning all records")
            store_metadata_list = [
                self._transform_nutro_store_metadata(row) for row in rows
            ]

            # Return wrapped response with metadata
            return NutroStoreMetaDataResponse(
                stores=store_metadata_list, total_count=len(store_metadata_list)
            )

        except HTTPException as e:
            # Convert HTTPException to 500 status
            logger.error(f"HTTPException fetching nutro store metadata: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error fetching store metadata: {str(e)}",
            )
        except SQLAlchemyError as e:
            # Convert SQLAlchemyError to descriptive HTTPException
            logger.error(f"Database error fetching nutro store metadata: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error fetching store metadata: {str(e)}",
            )
        except Exception:
            # Re-raise other exceptions
            raise

    def _transform_nutro_store_metadata(self, row) -> NutroStoreMetaData:
        """
        Transform database row to NutroStoreMetaData format

        Args:
            row: Database result row from stored procedure

        Returns:
            NutroStoreMetaData: Transformed individual store metadata object
        """
        try:
            # Build complete address from multiple fields
            # Format: LocationAddress, LocationCity, MailingState MailingZipCode
            location_address = getattr(row, "LocationAddress", None) or ""
            location_city = getattr(row, "LocationCity", None) or ""
            mailing_state = getattr(row, "MailingState", None) or ""
            mailing_zip = getattr(row, "MailingZipCode", None) or ""

            # Build address components list
            address_parts = []

            # Add location address if exists
            if location_address.strip():
                address_parts.append(location_address.strip())

            # Add location city if exists
            if location_city.strip():
                address_parts.append(location_city.strip())

            # Concatenate mailing state and zip with space, then add to address parts
            state_zip = ""
            if mailing_state.strip() and mailing_zip.strip():
                state_zip = f"{mailing_state.strip()} {mailing_zip.strip()}"
            elif mailing_state.strip():
                state_zip = mailing_state.strip()
            elif mailing_zip.strip():
                state_zip = mailing_zip.strip()

            if state_zip:
                address_parts.append(state_zip)

            # Join all address parts with comma and space
            complete_address = ", ".join(address_parts) if address_parts else None

            # Create and return the metadata object with mapped fields
            return NutroStoreMetaData(
                Address=complete_address,  # LocationAddress, LocationCity, MailingState MailingZipCode
                Phone=getattr(row, "Phone", None),  # Phone → Phone
                Contact=getattr(row, "StoreContact", None),  # Contact → StoreContact
                Chain=getattr(row, "ChainName", None),  # Chain → ChainName
                AccountType=getattr(
                    row, "AccountType", None
                ),  # Account type → AccountType
                CategoryLevelOne=getattr(
                    row, "AccountTypeDetail", None
                ),  # Category level one → AccountTypeDetail
                CategoryLevelTwo=getattr(
                    row, "ReportCategory", None
                ),  # Category level two → ReportCategory
                CategoryLevelThree=getattr(
                    row, "ReportCategoryDetail", None
                ),  # Category level three → ReportCategoryDetail
                Status=getattr(
                    row, "StoreStatusDesc", None
                ),  # Status → StoreStatusDesc
                TM=getattr(row, "TerritoryManager", None),  # TM → TerritoryManager
            )

        except Exception as e:
            logger.error(f"Error transforming nutro store metadata: {str(e)}")
            # Return a metadata object with None values if transformation fails
            return NutroStoreMetaData()

    def get_change_log(
        self,
        db: Session,
        nutrostore_id: int,
        date_filter: str = None,
        page: int = None,
        limit: int = None,
    ) -> ChangeLogResponse:
        """
        Get change log entries for a specific nutro store with optional date filtering and pagination

        Args:
            db: Database session
            nutrostore_id: The nutro store ID to fetch change logs for
            date_filter: Optional date filter in mmddyyyy format
            page: Optional page number (1-based)
            limit: Optional number of records per page

        Returns:
            ChangeLogResponse: Change log entries with pagination metadata

        Raises:
            HTTPException: 400 for invalid date format, 500 for database errors
        """
        try:
            logger.info(
                f"Fetching change log for NutroStoreID: {nutrostore_id}, date_filter: {date_filter}"
            )

            # Validate and parse date filter if provided
            filter_date = None
            if date_filter:
                filter_date = self._validate_and_parse_date(date_filter)

            # Execute the stored procedure to get change log data
            sql = text("""
                EXEC [nutrostore].[store_ChangeLog_Select]
                    @NutroStoreID = :nutrostore_id
            """)

            result = db.execute(sql, {"nutrostore_id": nutrostore_id})
            rows = result.fetchall()

            # Transform all database rows to change log entries
            all_change_entries = [self._transform_change_log_entry(row) for row in rows]

            # Apply date filtering if specified
            filtered_entries = all_change_entries
            if filter_date:
                filtered_entries = self._filter_by_date(all_change_entries, filter_date)
                logger.info(
                    f"Filtered {len(all_change_entries)} records to {len(filtered_entries)} matching date {date_filter}"
                )

            # Apply pagination if specified
            paginated_entries, pagination_meta = self._apply_pagination(
                filtered_entries, page, limit
            )

            # Return response with pagination metadata
            return ChangeLogResponse(
                changes=paginated_entries,
                total_count=len(filtered_entries),
                page=pagination_meta.get("page"),
                limit=pagination_meta.get("limit"),
                has_next=pagination_meta.get("has_next", False),
                has_previous=pagination_meta.get("has_previous", False),
            )

        except HTTPException as e:
            # Convert HTTPException to 500 status
            logger.error(f"HTTPException fetching change log: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error fetching change log: {str(e)}",
            )
        except SQLAlchemyError as e:
            # Convert SQLAlchemyError to descriptive HTTPException
            logger.error(f"Database error fetching change log: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error fetching change log: {str(e)}",
            )
        except Exception:
            # Re-raise other exceptions
            raise

    def _validate_and_parse_date(self, date_str: str) -> datetime:
        """
        Validate and parse date string in mmddyyyy format

        Args:
            date_str: Date string in mmddyyyy format

        Returns:
            datetime: Parsed date object

        Raises:
            HTTPException: 400 if date format is invalid
        """
        try:
            # Validate format and length
            if not date_str or len(date_str) != 8:
                raise ValueError("Date must be in mmddyyyy format (8 digits)")

            # Extract month, day, year
            month = int(date_str[:2])
            day = int(date_str[2:4])
            year = int(date_str[4:8])

            # Create datetime object (this will validate the date)
            parsed_date = datetime(year, month, day)
            logger.info(f"Successfully parsed date {date_str} to {parsed_date.date()}")
            return parsed_date

        except (ValueError, TypeError) as e:
            logger.error(f"Invalid date format {date_str}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid date format. Expected mmddyyyy format (e.g., 05272025), got: {date_str}",
            )

    def _filter_by_date(self, entries: list, filter_date: datetime) -> list:
        """
        Filter change log entries by date (ignoring time)

        Args:
            entries: List of ChangeLogEntry objects
            filter_date: Date to filter by

        Returns:
            list: Filtered entries matching the date
        """
        filtered = []
        target_date = filter_date.date()

        for entry in entries:
            try:
                # Parse the formatted date string back to datetime for comparison
                # Entry.ChangeDate is in "MM/DD/YYYY H:MM:SS AM/PM" format
                entry_datetime = datetime.strptime(
                    entry.ChangeDate, "%m/%d/%Y %I:%M:%S %p"
                )
                entry_date = entry_datetime.date()

                if entry_date == target_date:
                    filtered.append(entry)

            except Exception as e:
                logger.warning(
                    f"Error parsing date for filtering: {entry.ChangeDate}, error: {str(e)}"
                )
                continue

        return filtered

    def _apply_pagination(
        self, entries: list, page: int = None, limit: int = None
    ) -> tuple:
        """
        Apply pagination to change log entries

        Args:
            entries: List of entries to paginate
            page: Page number (1-based), None for no pagination
            limit: Number of records per page, None for all records

        Returns:
            tuple: (paginated_entries, pagination_metadata)
        """
        # If no pagination specified, return all entries
        if page is None or limit is None:
            return entries, {
                "page": None,
                "limit": None,
                "has_next": False,
                "has_previous": False,
            }

        # Calculate pagination
        total_count = len(entries)
        offset = (page - 1) * limit
        end_offset = offset + limit

        # Get paginated slice
        paginated_entries = entries[offset:end_offset]

        # Calculate pagination metadata
        has_next = end_offset < total_count
        has_previous = page > 1

        pagination_meta = {
            "page": page,
            "limit": limit,
            "has_next": has_next,
            "has_previous": has_previous,
        }

        logger.info(
            f"Applied pagination: page={page}, limit={limit}, total={total_count}, returned={len(paginated_entries)}"
        )
        return paginated_entries, pagination_meta

    def _transform_change_log_entry(self, row) -> ChangeLogEntry:
        """
        Transform database row to ChangeLogEntry format

        Args:
            row: Database result row from stored procedure

        Returns:
            ChangeLogEntry: Transformed change log entry
        """
        try:
            # Get the ChangeDate from database and convert format
            change_date_formatted = self._format_change_date(
                getattr(row, "ChangeDate", None)
            )

            # Create and return the change log entry with mapped fields
            return ChangeLogEntry(
                ChangeDate=change_date_formatted,  # Convert to MM/DD/YYYY H:MM:SS AM/PM format
                LastModifiedBy=getattr(
                    row, "LastModifiedBy", None
                ),  # LastModifiedBy → LastModifiedBy
                ChangeType=getattr(row, "ChangeType", None),  # ChangeType → ChangeType
            )

        except Exception as e:
            logger.error(f"Error transforming change log entry: {str(e)}")
            # Return entry with None values if transformation fails
            return ChangeLogEntry(ChangeDate="", LastModifiedBy=None, ChangeType=None)

    def _format_change_date(self, change_date) -> str:
        """
        Format change date from database format to UI format

        Args:
            change_date: Database datetime (e.g., 2025-05-27 20:25:46.123)

        Returns:
            str: Formatted date string (e.g., 05/27/2025 8:25:46 PM)
        """
        try:
            if not change_date:
                return ""

            # Handle different datetime types
            if hasattr(change_date, "strftime"):
                # It's already a datetime object
                formatted_date = change_date.strftime("%m/%d/%Y %I:%M:%S %p")
            else:
                # Try to parse string datetime
                if isinstance(change_date, str):
                    # Parse common database datetime formats
                    try:
                        # Try format: 2025-05-27 20:25:46.123
                        parsed_dt = datetime.strptime(
                            change_date[:19], "%Y-%m-%d %H:%M:%S"
                        )
                    except ValueError:
                        # Try format: 2025-05-27 20:25:46
                        parsed_dt = datetime.strptime(change_date, "%Y-%m-%d %H:%M:%S")

                    formatted_date = parsed_dt.strftime("%m/%d/%Y %I:%M:%S %p")
                else:
                    # Fallback: convert to string
                    formatted_date = str(change_date)

            logger.debug(f"Formatted date from {change_date} to {formatted_date}")
            return formatted_date

        except Exception as e:
            logger.warning(f"Error formatting date {change_date}: {str(e)}")
            # Return original value as string if formatting fails
            return str(change_date) if change_date else ""

    def get_mapping_details(
        self, db: Session, mapping_id: int, nutrostore_id: int
    ) -> MappingDetailResponse:
        """
        Get mapping details for a specific mapping ID using stored procedure

        Args:
            db: Database session
            mapping_id: The mapping ID (VCID) to fetch details for
            nutrostore_id: The nutro store ID (currently ignored as per requirements)

        Returns:
            MappingDetailResponse: Mapping detail information

        Raises:
            HTTPException: 500 for database errors
        """
        try:
            logger.info(
                f"Fetching mapping details for mapping_id: {mapping_id}, nutrostore_id: {nutrostore_id}"
            )

            # Execute the stored procedure to get mapping details
            # Note: nutrostore_id is ignored as per requirements, only mapping_id (VCID) is used
            sql = text("""
                EXEC [nutrostore].[store_mapping_Select]
                    @VCID = :mapping_id
            """)

            result = db.execute(sql, {"mapping_id": mapping_id})
            rows = result.fetchall()

            # Transform all database rows to mapping detail entries
            # Expected to return single record, but handling multiple for robustness
            mapping_details = [self._transform_mapping_detail(row) for row in rows]

            logger.info(f"Found {len(mapping_details)} mapping detail record(s)")

            # Return response with mapping details (empty list if no records found)
            return MappingDetailResponse(
                mappings=mapping_details, total_count=len(mapping_details)
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error fetching mapping details: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error fetching mapping details: {str(e)}",
            )
        except Exception as e:
            logger.error(f"Unexpected error fetching mapping details: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error fetching mapping details: {str(e)}",
            )

    def _transform_mapping_detail(self, row) -> MappingDetail:
        """
        Transform database row to MappingDetail format

        Args:
            row: Database result row from stored procedure

        Returns:
            MappingDetail: Transformed mapping detail object
        """
        try:
            # Build complete address from multiple fields
            # Format: DistStoreAddress, DistStoreCity, DistStoreState, DistStoreZip (comma-separated)
            dist_store_address = getattr(row, "DistStoreAddress", None) or ""
            dist_store_city = getattr(row, "DistStoreCity", None) or ""
            dist_store_state = getattr(row, "DistStoreState", None) or ""
            dist_store_zip = getattr(row, "DistStoreZip", None) or ""

            # Build address components list
            address_parts = []

            # Add each component if it exists and is not empty
            if dist_store_address.strip():
                address_parts.append(dist_store_address.strip())
            if dist_store_city.strip():
                address_parts.append(dist_store_city.strip())
            if dist_store_state.strip():
                address_parts.append(dist_store_state.strip())
            if dist_store_zip.strip():
                address_parts.append(dist_store_zip.strip())

            # Join all address parts with comma and space
            complete_address = ", ".join(address_parts) if address_parts else None

            # Format transaction dates to MM/DD/YYYY format
            first_trans_date = self._format_transaction_date(
                getattr(row, "FirstTransactionDate", None)
            )
            last_trans_date = self._format_transaction_date(
                getattr(row, "LastTransactionDate", None)
            )

            # Convert IsPending integer (0/1) to boolean (true/false)
            is_pending_value = getattr(row, "IsPending", None)
            is_pending_bool = None
            if is_pending_value is not None:
                try:
                    is_pending_bool = bool(int(is_pending_value))
                except (ValueError, TypeError):
                    logger.warning(
                        f"Could not convert IsPending value to boolean: {is_pending_value}"
                    )
                    is_pending_bool = None

            # Create and return the mapping detail object with mapped fields
            return MappingDetail(
                DataSource=getattr(row, "DataSource", None),  # DataSource → DataSource
                DataStoreName=getattr(
                    row, "DistStoreName", None
                ),  # DistStoreName → DataStoreName
                DataStoreID=getattr(
                    row, "DistStoreID", None
                ),  # DistStoreID → DataStoreID
                Address=complete_address,  # DistStoreAddress,DistStoreCity,DistStoreState,DistStoreZip → Address
                CustomerType=getattr(
                    row, "DistCustomerType", None
                ),  # DistCustomerType → CustomerType
                Warehouse=getattr(
                    row, "DistWarehouse", None
                ),  # DistWarehouse → Warehouse
                Rep=getattr(row, "DistRep", None),  # DistRep → Rep
                Country=getattr(
                    row, "DistStoreCountry", None
                ),  # DistStoreCountry → Country
                FirstTransDate=first_trans_date,  # FirstTransactionDate → FirstTransDate (MM/DD/YYYY)
                LastTransDate=last_trans_date,  # LastTransactionDate → LastTransDate (MM/DD/YYYY)
                Last4WeekPOS=getattr(
                    row, "L4POSSales", None
                ),  # L4POSSales → Last4WeekPOS
                Last52WeekPOS=getattr(
                    row, "L52POSSales", None
                ),  # L52POSSales → Last52WeekPOS
                Total2010On=getattr(
                    row, "TotalPOSSales", None
                ),  # TotalPOSSales → Total2010On
                Phone=getattr(row, "DistStorePhone", None),  # DistStorePhone → Phone
                MappingStatus=getattr(
                    row, "StoreName", None
                ),  # StoreName → MappingStatus
                IsPending=is_pending_bool,  # IsPending (0/1) → IsPending (true/false)
            )

        except Exception as e:
            logger.error(f"Error transforming mapping detail: {str(e)}")
            # Return a mapping detail with None values if transformation fails
            return MappingDetail()

    def _format_transaction_date(self, transaction_date) -> str:
        """
        Format transaction date to MM/DD/YYYY format

        Args:
            transaction_date: Database datetime value

        Returns:
            str: Formatted date string in MM/DD/YYYY format or empty string
        """
        try:
            if not transaction_date:
                return ""

            # Handle different datetime types
            if hasattr(transaction_date, "strftime"):
                # It's already a datetime object
                formatted_date = transaction_date.strftime("%m/%d/%Y")
            else:
                # Try to parse string datetime
                if isinstance(transaction_date, str):
                    # Parse common database datetime formats
                    try:
                        # Try format: 2025-05-27 20:25:46.123 or 2025-05-27
                        if " " in transaction_date:
                            parsed_dt = datetime.strptime(
                                transaction_date[:10], "%Y-%m-%d"
                            )
                        else:
                            parsed_dt = datetime.strptime(transaction_date, "%Y-%m-%d")
                    except ValueError:
                        # Try other common formats
                        parsed_dt = datetime.strptime(transaction_date[:10], "%Y-%m-%d")

                    formatted_date = parsed_dt.strftime("%m/%d/%Y")
                else:
                    # Fallback: convert to string
                    formatted_date = str(transaction_date)

            logger.debug(
                f"Formatted transaction date from {transaction_date} to {formatted_date}"
            )
            return formatted_date

        except Exception as e:
            logger.warning(
                f"Error formatting transaction date {transaction_date}: {str(e)}"
            )
            # Return original value as string if formatting fails
            return str(transaction_date) if transaction_date else ""

    def get_store_suggestions(
        self, db: Session, vcid: int, page: int = None, limit: int = None
    ) -> StoreSuggestionResponse:
        """
        Get store suggestions for a specific VCID using stored procedure with pagination

        Args:
            db: Database session
            vcid: The VCID to fetch store suggestions for
            page: Optional page number (1-based)
            limit: Optional number of records per page

        Returns:
            StoreSuggestionResponse: Store suggestions with pagination metadata

        Raises:
            HTTPException: 500 for database errors
        """
        try:
            logger.info(
                f"Fetching store suggestions for VCID: {vcid}, page: {page}, limit: {limit}"
            )

            # Execute the stored procedure to get store suggestions
            sql = text("""
                EXEC [nutrostore].[store_mapping_StoreSuggestions]
                    @VCID = :vcid
            """)

            result = db.execute(sql, {"vcid": vcid})
            rows = result.fetchall()

            # Transform all database rows to store suggestion entries
            all_suggestions = [self._transform_store_suggestion(row) for row in rows]

            logger.info(f"Found {len(all_suggestions)} store suggestion record(s)")

            # Apply pagination if specified
            paginated_suggestions, pagination_meta = self._apply_pagination(
                all_suggestions, page, limit
            )

            # Return response with pagination metadata
            return StoreSuggestionResponse(
                suggestions=paginated_suggestions,
                total_count=len(all_suggestions),
                page=pagination_meta.get("page"),
                limit=pagination_meta.get("limit"),
                has_next=pagination_meta.get("has_next", False),
                has_previous=pagination_meta.get("has_previous", False),
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error fetching store suggestions: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error fetching store suggestions: {str(e)}",
            )
        except Exception as e:
            logger.error(f"Unexpected error fetching store suggestions: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error fetching store suggestions: {str(e)}",
            )

    def _transform_store_suggestion(self, row) -> Dict[str, Any]:
        """
        Transform database row to store suggestion dictionary format

        Args:
            row: Database result row from stored procedure

        Returns:
            Dict: Transformed store suggestion dictionary with exact UI field names
        """
        try:
            # Get MatchScore and convert to float if possible
            match_score = getattr(row, "MatchScore", None)
            score_float = None
            if match_score is not None:
                try:
                    score_float = float(match_score)
                except (ValueError, TypeError):
                    logger.warning(
                        f"Could not convert MatchScore to float: {match_score}"
                    )
                    score_float = None

            # Create dictionary with exact UI field names (including spaces)
            return {
                "SCORE": score_float,  # MatchScore → SCORE (float)
                "NUTRO ID": getattr(
                    row, "NutroStoreID", None
                ),  # NutroStoreID → NUTRO ID
                "STORE": getattr(row, "StoreName", None),  # StoreName → STORE
                "ADDRESS": getattr(
                    row, "LocationAddress", None
                ),  # LocationAddress → ADDRESS
                "CITY": getattr(row, "LocationCity", None),  # LocationCity → CITY
                "STATE": getattr(row, "LocationState", None),  # LocationState → STATE
                "ZIP": getattr(row, "LocationZipCode", None),  # LocationZipCode → ZIP
                "PHONE": getattr(row, "Phone", None),  # Phone → PHONE
                "STATUS": getattr(row, "StoreStatus", None),  # StoreStatus → STATUS
                "TERRITORY MANAGER": getattr(
                    row, "TerritoryManager", None
                ),  # TerritoryManager → TERRITORY MANAGER
            }

        except Exception as e:
            logger.error(f"Error transforming store suggestion: {str(e)}")
            # Return a dictionary with None values if transformation fails
            return {
                "SCORE": None,
                "NUTRO ID": None,
                "STORE": None,
                "ADDRESS": None,
                "CITY": None,
                "STATE": None,
                "ZIP": None,
                "PHONE": None,
                "STATUS": None,
                "TERRITORY MANAGER": None,
            }

    def search_dist_stores_or_mapped_stores(
        self,
        db: Session,
        search_params: DistStoreSearchRequest,
        page: int = 1,
        limit: int = 100,
    ) -> DistStoreSearchResponse:
        """
        Search dist stores using the store_mapping_Search stored procedure with pagination

        Args:
            db: Database session
            search_params: Search parameters
            page: Page number (1-based)
            limit: Number of records per page

        Returns:
            DistStoreSearchResponse: Paginated search results
        """
        try:
            # Convert empty strings to None for NULL parameters
            params = self._prepare_dist_store_search_params(search_params)

            # Calculate offset for pagination
            offset = (page - 1) * limit

            # Execute stored procedure with pagination
            stores = self._execute_dist_store_search(db, params, offset, limit)

            # Get total count for pagination metadata
            total_count = self._get_dist_store_total_count(db, params)

            # Convert results to response objects
            store_responses = [
                self._transform_dist_store_response(row) for row in stores
            ]

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return DistStoreSearchResponse(
                stores=store_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except Exception as e:
            logger.error(f"Error searching dist stores: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching dist stores: {str(e)}",
            )

    def search_dist_stores_or_mapped_stores_or_logic(
        self,
        db: Session,
        generic_search: str,
        page: int = 1,
        limit: int = 100,
    ) -> DistStoreSearchResponse:
        """
        Search dist stores using OR logic with sequential field-specific searches

        This method performs sequential searches across all dist store fields and combines
        the results, providing OR functionality instead of AND filtering.

        Args:
            db: Database session
            generic_search: Search term to apply across all fields
            page: Page number (1-based)
            limit: Number of records per page

        Returns:
            DistStoreSearchResponse: Paginated search results with deduplicated stores
        """
        try:
            # Define all searchable fields for dist stores
            search_fields = [
                "NutroStoreID",
                "DataSource",
                "DistStoreID",
                "DistStoreName",
                "DistStoreAddress",
                "DistStoreCity",
                "DistStoreState",
                "DistStoreZip",
                "DistStorePhone",
            ]

            # Execute sequential searches for each field
            all_results = self._execute_sequential_dist_field_searches(
                db, generic_search, search_fields
            )

            # Combine and deduplicate results
            combined_stores = self._deduplicate_dist_store_results(all_results)

            # Apply pagination to combined results
            total_count = len(combined_stores)
            offset = (page - 1) * limit
            paginated_stores = combined_stores[offset : offset + limit]

            # Convert to response objects
            store_responses = [
                self._transform_dist_store_response(row) for row in paginated_stores
            ]

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return DistStoreSearchResponse(
                stores=store_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except Exception as e:
            logger.error(f"Error in OR-based dist store search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching dist stores with OR logic: {str(e)}",
            )

    def _prepare_dist_store_search_params(
        self, search_params: DistStoreSearchRequest
    ) -> Dict[str, Any]:
        """
        Convert search parameters to dictionary and handle empty strings

        Args:
            search_params: Search parameters from request

        Returns:
            Dict with None values for empty strings
        """
        params = search_params.model_dump()

        # Convert empty strings to None for NULL parameters
        for key, value in params.items():
            if value == "":
                params[key] = None

        return params

    def _execute_dist_store_search(
        self, db: Session, params: Dict[str, Any], offset: int, limit: int
    ) -> List:
        """
        Execute the store_mapping_Search stored procedure with pagination

        Args:
            db: Database session
            params: Search parameters
            offset: Number of records to skip
            limit: Number of records to return

        Returns:
            List of result rows
        """
        sql = text("""
            EXEC [nutrostore].[store_mapping_Search]
                @NutroStoreID = :NutroStoreID,
                @DataSource = :DataSource,
                @DistStoreID = :DistStoreID,
                @DistStoreName = :DistStoreName,
                @DistStoreAddress = :DistStoreAddress,
                @DistStoreCity = :DistStoreCity,
                @DistStoreState = :DistStoreState,
                @DistStoreZip = :DistStoreZip,
                @DistStorePhone = :DistStorePhone
        """)

        try:
            logger.info(f"Executing store_mapping_Search with params: {params}")
            result = db.execute(sql, params)
            all_rows = result.fetchall()

            # Apply pagination in Python
            return all_rows[offset : offset + limit]
        except SQLAlchemyError as e:
            logger.error(f"Database error executing store_mapping_Search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error executing stored procedure: {str(e)}",
            )

    def _get_dist_store_total_count(self, db: Session, params: Dict[str, Any]) -> int:
        """
        Get total count of records matching search criteria

        Args:
            db: Database session
            params: Search parameters

        Returns:
            Total count of matching records
        """
        sql = text("""
            EXEC [nutrostore].[store_mapping_Search]
                @NutroStoreID = :NutroStoreID,
                @DataSource = :DataSource,
                @DistStoreID = :DistStoreID,
                @DistStoreName = :DistStoreName,
                @DistStoreAddress = :DistStoreAddress,
                @DistStoreCity = :DistStoreCity,
                @DistStoreState = :DistStoreState,
                @DistStoreZip = :DistStoreZip,
                @DistStorePhone = :DistStorePhone
        """)

        try:
            result = db.execute(sql, params)
            return len(result.fetchall())
        except SQLAlchemyError as e:
            logger.error(
                f"Database error getting count for store_mapping_Search: {str(e)}"
            )
            return 0

    def _transform_dist_store_response(self, row) -> DistStoreResponse:
        """
        Convert database row to DistStoreResponse format

        Args:
            row: Database result row

        Returns:
            DistStoreResponse: Transformed response
        """
        try:
            # Combine city, state, zip into a single location string
            city = getattr(row, "DistStoreCity", None) or ""
            state = getattr(row, "DistStoreState", None) or ""
            zip_code = getattr(row, "DistStoreZip", None) or ""

            location_parts = []
            if city.strip():
                location_parts.append(city.strip())
            if state.strip():
                location_parts.append(state.strip())
            if zip_code.strip():
                if location_parts:
                    location_parts[-1] = f"{location_parts[-1]} {zip_code.strip()}"
                else:
                    location_parts.append(zip_code.strip())

            city_state_zip = ", ".join(location_parts) if location_parts else None

            # Format dates to MM/DD/YYYY if they exist
            first_dt = self._format_transaction_date(getattr(row, "FirstTransactionDate", None))
            last_dt = self._format_transaction_date(getattr(row, "LastTransactionDate", None))

            # Convert IsPending to boolean
            is_pending_value = getattr(row, "IsPending", None)
            is_pending = None
            if is_pending_value is not None:
                try:
                    is_pending = bool(int(is_pending_value))
                except (ValueError, TypeError):
                    logger.warning(
                        f"Could not convert IsPending value to boolean: {is_pending_value}"
                    )
                    is_pending = None

            return DistStoreResponse(
                DataSource=getattr(row, "DataSource", None),
                StoreID=getattr(row, "DistStoreID", None),
                StoreDesc=getattr(row, "DistStoreName", None),
                CityStateZip=city_state_zip,
                FirstTransactionDate=first_dt,
                LastTransactionDate=last_dt,
                Last4POS=getattr(row, "L4POSSales", None),
                Last52POS=getattr(row, "L52POSSales", None),
                TotalPOSSales=getattr(row, "TotalPOSSales", None),
                MappedTo=getattr(row, "StoreName", None),
                VCID=getattr(row, "VCID", None),
                IsPending=is_pending,
            )
        except Exception as e:
            logger.error(f"Error transforming dist store response: {str(e)}")
            return DistStoreResponse()

    def search_unmapped_stores(
        self,
        db: Session,
        search_params: UnmappedStoreSearchRequest,
        page: int = 1,
        limit: int = 100,
    ) -> UnmappedStoreSearchResponse:
        """
        Search unmapped stores using the store_mapping_Unknowns stored procedure with pagination

        Args:
            db: Database session
            search_params: Search parameters
            page: Page number (1-based)
            limit: Number of records per page

        Returns:
            UnmappedStoreSearchResponse: Paginated search results
        """
        try:
            # Convert empty strings to None for NULL parameters
            params = self._prepare_unmapped_store_search_params(search_params)

            # Calculate offset for pagination
            offset = (page - 1) * limit

            # Execute stored procedure with pagination
            stores = self._execute_unmapped_store_search(db, params, offset, limit)

            # Get total count for pagination metadata
            total_count = self._get_unmapped_store_total_count(db, params)

            # Convert results to response objects
            store_responses = [
                self._transform_unmapped_store_response(row) for row in stores
            ]

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return UnmappedStoreSearchResponse(
                stores=store_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except Exception as e:
            logger.error(f"Error searching unmapped stores: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching unmapped stores: {str(e)}",
            )

    def search_unmapped_stores_or_logic(
        self,
        db: Session,
        generic_search: str,
        page: int = 1,
        limit: int = 100,
    ) -> UnmappedStoreSearchResponse:
        """
        Search unmapped stores using OR logic with sequential field-specific searches

        This method performs sequential searches across all unmapped store fields and combines
        the results, providing OR functionality instead of AND filtering.

        Args:
            db: Database session
            generic_search: Search term to apply across all fields
            page: Page number (1-based)
            limit: Number of records per page

        Returns:
            UnmappedStoreSearchResponse: Paginated search results with deduplicated stores
        """
        try:
            # Define all searchable fields for unmapped stores
            search_fields = [
                "DataSource",
                "DistStoreID",
                "DistStoreName",
                "DistStoreAddress",
                "DistStoreCity",
                "DistStoreState",
                "DistStoreZip",
                "DistStorePhone",
            ]

            # Execute sequential searches for each field
            all_results = self._execute_sequential_unmapped_field_searches(
                db, generic_search, search_fields
            )

            # Combine and deduplicate results
            combined_stores = self._deduplicate_unmapped_store_results(all_results)

            # Apply pagination to combined results
            total_count = len(combined_stores)
            offset = (page - 1) * limit
            paginated_stores = combined_stores[offset : offset + limit]

            # Convert to response objects
            store_responses = [
                self._transform_unmapped_store_response(row) for row in paginated_stores
            ]

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return UnmappedStoreSearchResponse(
                stores=store_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except Exception as e:
            logger.error(f"Error in OR-based unmapped store search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching unmapped stores with OR logic: {str(e)}",
            )

    def _prepare_unmapped_store_search_params(
        self, search_params: UnmappedStoreSearchRequest
    ) -> Dict[str, Any]:
        """
        Convert search parameters to dictionary and handle empty strings

        Args:
            search_params: Search parameters from request

        Returns:
            Dict with None values for empty strings
        """
        params = search_params.model_dump()

        # Convert empty strings to None for NULL parameters
        for key, value in params.items():
            if value == "":
                params[key] = None

        return params

    def _execute_unmapped_store_search(
        self, db: Session, params: Dict[str, Any], offset: int, limit: int
    ) -> List:
        """
        Execute the store_mapping_Unknowns stored procedure with pagination

        Args:
            db: Database session
            params: Search parameters
            offset: Number of records to skip
            limit: Number of records to return

        Returns:
            List of result rows
        """
        sql = text("""
            EXEC [nutrostore].[store_mapping_Unknowns]
                @DataSource = :DataSource,
                @DistStoreID = :DistStoreID,
                @DistStoreName = :DistStoreName,
                @DistStoreAddress = :DistStoreAddress,
                @DistStoreCity = :DistStoreCity,
                @DistStoreState = :DistStoreState,
                @DistStoreZip = :DistStoreZip,
                @DistStorePhone = :DistStorePhone
        """)

        try:
            logger.info(f"Executing store_mapping_Unknowns with params: {params}")
            result = db.execute(sql, params)
            all_rows = result.fetchall()

            # Apply pagination in Python
            return all_rows[offset : offset + limit]
        except SQLAlchemyError as e:
            logger.error(f"Database error executing store_mapping_Unknowns: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error executing stored procedure: {str(e)}",
            )

    def _get_unmapped_store_total_count(
        self, db: Session, params: Dict[str, Any]
    ) -> int:
        """
        Get total count of records matching search criteria

        Args:
            db: Database session
            params: Search parameters

        Returns:
            Total count of matching records
        """
        sql = text("""
            EXEC [nutrostore].[store_mapping_Unknowns]
                @DataSource = :DataSource,
                @DistStoreID = :DistStoreID,
                @DistStoreName = :DistStoreName,
                @DistStoreAddress = :DistStoreAddress,
                @DistStoreCity = :DistStoreCity,
                @DistStoreState = :DistStoreState,
                @DistStoreZip = :DistStoreZip,
                @DistStorePhone = :DistStorePhone
        """)

        try:
            result = db.execute(sql, params)
            return len(result.fetchall())
        except SQLAlchemyError as e:
            logger.error(
                f"Database error getting count for store_mapping_Unknowns: {str(e)}"
            )
            return 0

    def _transform_unmapped_store_response(self, row) -> UnmappedStoreResponse:
        """
        Convert database row to UnmappedStoreResponse format

        Args:
            row: Database result row

        Returns:
            UnmappedStoreResponse: Transformed response
        """
        try:
            # Combine city, state, zip into a single location string
            city = getattr(row, "DistStoreCity", None) or ""
            state = getattr(row, "DistStoreState", None) or ""
            zip_code = getattr(row, "DistStoreZip", None) or ""

            location_parts = []
            if city.strip():
                location_parts.append(city.strip())
            if state.strip():
                location_parts.append(state.strip())
            if zip_code.strip():
                if location_parts:
                    location_parts[-1] = f"{location_parts[-1]} {zip_code.strip()}"
                else:
                    location_parts.append(zip_code.strip())

            city_state_zip = ", ".join(location_parts) if location_parts else None

            # Format dates to MM/DD/YYYY if they exist
            first_dt = self._format_transaction_date(getattr(row, "FirstDt", None))
            last_dt = self._format_transaction_date(getattr(row, "LastDt", None))

            return UnmappedStoreResponse(
                DataSource=getattr(row, "DataSource", None),
                StoreID=getattr(row, "DistStoreID", None),
                StoreDesc=getattr(row, "DistStoreName", None),
                CityStateZip=city_state_zip,
                FirstDt=first_dt,
                LastDt=last_dt,
                Since51525=getattr(row, "Since51525", None),
                Since61324=getattr(row, "Since61324", None),
                Since1110=getattr(row, "Since1110", None),
                Status=getattr(row, "Status", None),
                VCID=getattr(row, "VCID", None),
            )
        except Exception as e:
            logger.error(f"Error transforming unmapped store response: {str(e)}")
            return UnmappedStoreResponse()

    def _execute_sequential_unmapped_field_searches(
        self, db: Session, generic_search: str, search_fields: List[str]
    ) -> List:
        """
        Execute sequential searches for each unmapped store field using a simple for loop

        Args:
            db: Database session
            generic_search: Search term to apply to each field
            search_fields: List of field names to search

        Returns:
            List of all result rows from each field search
        """
        all_results = []

        # Execute searches one by one for each field
        for field_name in search_fields:
            try:
                field_results = self._execute_single_unmapped_field_search(
                    db, generic_search, field_name
                )
                all_results.extend(field_results)
                logger.info(
                    f"Unmapped store search for field {field_name} returned {len(field_results)} results"
                )
            except Exception as e:
                logger.warning(
                    f"Unmapped store search failed for field {field_name}: {str(e)}"
                )
                # Continue with other fields even if one fails
                continue

        return all_results

    def _execute_single_unmapped_field_search(
        self, db: Session, search_term: str, field_name: str
    ) -> List:
        """
        Execute search for a single unmapped store field

        Args:
            db: Database session
            search_term: Term to search for
            field_name: Name of the field to search in

        Returns:
            List of result rows from the search
        """
        try:
            # Create search parameters with only the specified field populated
            params = {
                "DataSource": None,
                "DistStoreID": None,
                "DistStoreName": None,
                "DistStoreAddress": None,
                "DistStoreCity": None,
                "DistStoreState": None,
                "DistStoreZip": None,
                "DistStorePhone": None,
            }

            # Set the specific field to the search term
            params[field_name] = search_term

            # Execute the stored procedure
            sql = text("""
                EXEC [nutrostore].[store_mapping_Unknowns]
                    @DataSource = :DataSource,
                    @DistStoreID = :DistStoreID,
                    @DistStoreName = :DistStoreName,
                    @DistStoreAddress = :DistStoreAddress,
                    @DistStoreCity = :DistStoreCity,
                    @DistStoreState = :DistStoreState,
                    @DistStoreZip = :DistStoreZip,
                    @DistStorePhone = :DistStorePhone
            """)

            result = db.execute(sql, params)
            return result.fetchall()

        except Exception as e:
            logger.error(f"Error searching unmapped store field {field_name}: {str(e)}")
            return []

    def _deduplicate_unmapped_store_results(self, all_results: List) -> List:
        """
        Remove duplicate unmapped stores from combined results based on DistStoreID and DataSource

        Args:
            all_results: Combined list of all search results

        Returns:
            List of unique unmapped store records
        """
        seen_combinations = set()
        unique_stores = []

        for row in all_results:
            dist_store_id = getattr(row, "DistStoreID", None)
            data_source = getattr(row, "DataSource", None)

            # Create a unique key combining DistStoreID and DataSource
            unique_key = (dist_store_id, data_source)

            # Skip rows without required fields or duplicates
            if (
                dist_store_id is None
                or data_source is None
                or unique_key in seen_combinations
            ):
                continue

            seen_combinations.add(unique_key)
            unique_stores.append(row)

        return unique_stores

    def _execute_sequential_dist_field_searches(
        self, db: Session, generic_search: str, search_fields: List[str]
    ) -> List:
        """
        Execute sequential searches for each dist store field using a simple for loop

        Args:
            db: Database session
            generic_search: Search term to apply to each field
            search_fields: List of field names to search

        Returns:
            List of all result rows from each field search
        """
        all_results = []

        # Execute searches one by one for each field
        for field_name in search_fields:
            try:
                field_results = self._execute_single_dist_field_search(
                    db, generic_search, field_name
                )
                all_results.extend(field_results)
                logger.info(
                    f"Dist store search for field {field_name} returned {len(field_results)} results"
                )
            except Exception as e:
                logger.warning(
                    f"Dist store search failed for field {field_name}: {str(e)}"
                )
                # Continue with other fields even if one fails
                continue

        return all_results

    def _execute_single_dist_field_search(
        self, db: Session, search_term: str, field_name: str
    ) -> List:
        """
        Execute search for a single dist store field

        Args:
            db: Database session
            search_term: Term to search for
            field_name: Name of the field to search in

        Returns:
            List of result rows from the search
        """
        try:
            # Create search parameters with only the specified field populated
            params = {
                "NutroStoreID": None,
                "DataSource": None,
                "DistStoreID": None,
                "DistStoreName": None,
                "DistStoreAddress": None,
                "DistStoreCity": None,
                "DistStoreState": None,
                "DistStoreZip": None,
                "DistStorePhone": None,
            }

            # Set the specific field to the search term
            params[field_name] = search_term

            # Execute the stored procedure
            sql = text("""
                EXEC [nutrostore].[store_mapping_Search]
                    @NutroStoreID = :NutroStoreID,
                    @DataSource = :DataSource,
                    @DistStoreID = :DistStoreID,
                    @DistStoreName = :DistStoreName,
                    @DistStoreAddress = :DistStoreAddress,
                    @DistStoreCity = :DistStoreCity,
                    @DistStoreState = :DistStoreState,
                    @DistStoreZip = :DistStoreZip,
                    @DistStorePhone = :DistStorePhone
            """)

            result = db.execute(sql, params)
            return result.fetchall()

        except Exception as e:
            logger.error(f"Error searching dist store field {field_name}: {str(e)}")
            return []

    def _deduplicate_dist_store_results(self, all_results: List) -> List:
        """
        Remove duplicate dist stores from combined results based on DistStoreID and DataSource

        Args:
            all_results: Combined list of all search results

        Returns:
            List of unique dist store records
        """
        seen_combinations = set()
        unique_stores = []

        for row in all_results:
            dist_store_id = getattr(row, "DistStoreID", None)
            data_source = getattr(row, "DataSource", None)

            # Create a unique key combining DistStoreID and DataSource
            unique_key = (dist_store_id, data_source)

            # Skip rows without required fields or duplicates
            if (
                dist_store_id is None
                or data_source is None
                or unique_key in seen_combinations
            ):
                continue

            seen_combinations.add(unique_key)
            unique_stores.append(row)

        return unique_stores

    def create_store(
        self,
        db: Session,
        store_data: StoreCreateRequest,
    ) -> StoreCreateResponse:
        """
        Create a new store using the store_Insert stored procedure

        This method executes the [dbo].[store_Insert] stored procedure with all the provided
        store information and returns the generated NutroStoreID along with success status.

        Args:
            db: Database session
            store_data: Store creation request containing all store information

        Returns:
            StoreCreateResponse: Response containing success status, message, and generated NutroStoreID

        Raises:
            HTTPException: 400 for validation errors, 500 for database errors
        """
        try:
            logger.info(
                f"Creating new store with data: StoreName={store_data.StoreName}, ChainName={store_data.ChainName}"
            )

            # Prepare parameters for the stored procedure
            # Convert Pydantic model to dictionary and handle None values
            params = self._prepare_store_create_params(store_data)

            # Execute the stored procedure with output parameter
            self._execute_store_insert(db, params)

            return StoreCreateResponse(
                success=True, message="Store created successfully"
            )

        except HTTPException:
            # Re-raise HTTP exceptions (validation errors, etc.)
            raise
        except SQLAlchemyError as e:
            logger.error(f"Database error creating store: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error creating store: {str(e)}",
            )
        except Exception as e:
            logger.error(f"Unexpected error creating store: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error creating store: {str(e)}",
            )

    def _prepare_store_create_params(
        self, store_data: StoreCreateRequest
    ) -> Dict[str, Any]:
        """
        Prepare parameters for store creation stored procedure

        Converts the Pydantic model to a dictionary and handles data type conversions
        and None value handling for the stored procedure parameters.

        Args:
            store_data: Store creation request data

        Returns:
            Dict containing prepared parameters for the stored procedure
        """
        # Convert Pydantic model to dictionary
        params = store_data.model_dump()

        # Handle special data type conversions and None values
        # Convert empty strings to None for NULL parameters
        for key, value in params.items():
            if value == "":
                params[key] = None

        # Ensure boolean fields are properly handled
        if params.get("IsCallTarget") is not None:
            params["IsCallTarget"] = bool(params["IsCallTarget"])
        if params.get("IsDemoTarget") is not None:
            params["IsDemoTarget"] = bool(params["IsDemoTarget"])

        # Handle date conversion if needed
        if params.get("ISODate") is not None and hasattr(
            params["ISODate"], "isoformat"
        ):
            params["ISODate"] = params["ISODate"].isoformat()

        logger.debug(f"Prepared store creation parameters: {params}")
        return params

    def _execute_store_insert(
        self, db: Session, params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute the store_Insert stored procedure with output parameter

        This method calls the [dbo].[store_Insert] stored procedure and captures
        the output parameter (NutroStoreID) that is generated during insertion.

        Args:
            db: Database session
            params: Dictionary of parameters for the stored procedure

        Returns:
            Dict containing the output parameter values

        Raises:
            SQLAlchemyError: For database-related errors
        """
        try:
            # Build the stored procedure call with all parameters
            # Note: Using OUTPUT parameter to capture the generated NutroStoreID
            sql = text("""
                DECLARE @NutroStoreID_Output int;

                EXEC [dbo].[store_Insert]
                    @StoreName = :StoreName,
                    @ChainName = :ChainName,
                    @StoreNumber = :StoreNumber,
                    @LocationType = :LocationType,
                    @LocationTypeDetail = :LocationTypeDetail,
                    @AccountType = :AccountType,
                    @AccountTypeDetail = :AccountTypeDetail,
                    @ReportCategory = :ReportCategory,
                    @ReportCategoryDetail = :ReportCategoryDetail,
                    @StoreStatus = :StoreStatus,
                    @LocationAddress = :LocationAddress,
                    @LocationCity = :LocationCity,
                    @LocationState = :LocationState,
                    @LocationZipCode = :LocationZipCode,
                    @LocationCountry = :LocationCountry,
                    @MailingAddress = :MailingAddress,
                    @MailingCity = :MailingCity,
                    @MailingState = :MailingState,
                    @MailingZipCode = :MailingZipCode,
                    @MailingCountry = :MailingCountry,
                    @StoreContact = :StoreContact,
                    @Phone = :Phone,
                    @Fax = :Fax,
                    @EmailAddress = :EmailAddress,
                    @WebAddress = :WebAddress,
                    @ISODate = :ISODate,
                    @Size_Override = :Size_Override,
                    @BuyingGroup = :BuyingGroup,
                    @Distribution_Notes = :Distribution_Notes,
                    @AccountTM = :AccountTM,
                    @IsCallTarget = :IsCallTarget,
                    @Priority = :Priority,
                    @CallFrequency = :CallFrequency,
                    @WhereToBuyBrands = :WhereToBuyBrands,
                    @ProgramEnrollment = :ProgramEnrollment,
                    @StoreNotes = :StoreNotes,
                    @DemoTerritoryDivID = :DemoTerritoryDivID,
                    @DemoMarket = :DemoMarket,
                    @IsDemoTarget = :IsDemoTarget,
                    @DemoPriority = :DemoPriority,
                    @DemoFrequency = :DemoFrequency,
                    @DemoNotes = :DemoNotes,
                    @RequestBy = :RequestBy,
                    @RequestNotes = :RequestNotes,
                    @VCID = :VCID,
                    @NutroStoreID = @NutroStoreID_Output OUTPUT;

                SELECT @NutroStoreID_Output as NutroStoreID;
            """)

            logger.info("Executing store_Insert stored procedure")
            result = db.execute(sql, params)

            # Fetch the output parameter value
            output_row = result.fetchone()
            if output_row:
                nutro_store_id = getattr(output_row, "NutroStoreID", None)
                logger.info(
                    f"Store insert completed, generated NutroStoreID: {nutro_store_id}"
                )
                return {"NutroStoreID": nutro_store_id}
            else:
                logger.warning("No output returned from store_Insert procedure")
                return {"NutroStoreID": None}

        except SQLAlchemyError as e:
            logger.error(f"Database error executing store_Insert: {str(e)}")
            raise SQLAlchemyError(f"Unexpected error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error executing store_Insert: {str(e)}")
            raise

    def update_store_mapping(self, db: Session, vcid: int, NutrostoreID: int):
        """
        Update store mapping metadata.
        """
        try:
            if NutrostoreID:
                # Update nutrostore.tsStoreMap table with the fetched nutrostoreid and the provided vcid.
                update_query = text(
                    "UPDATE nutrostore.tsStoreMap SET nutrostoreid = :nutrostoreid WHERE vcid = :vcid"
                )
                db.execute(update_query, {"nutrostoreid": NutrostoreID, "vcid": vcid})

                # Execute additional change request update query
                logger.info(
                    f"Executing change request update for VCID: {vcid}, NutroStoreID: {NutrostoreID}"
                )
                change_request_query = text("""
                    ;WITH r AS (
                        SELECT ToNutroStoreID, VCID, RecID,
                            RANK() OVER (PARTITION BY VCID ORDER BY RecID DESC) AS CRank
                        FROM nutrostore.ttStoreMap_ChangeRequests
                        WHERE Complete = 0
                    )
                    UPDATE r
                    SET ToNutroStoreID = :nutrostoreid
                    WHERE r.CRank = 1 AND r.vcid = :vcid
                """)

                change_result = db.execute(
                    change_request_query, {"nutrostoreid": NutrostoreID, "vcid": vcid}
                )

                # Log the number of rows affected by the change request update
                rows_affected = change_result.rowcount
                logger.info(
                    f"Change request update affected {rows_affected} rows for VCID: {vcid}"
                )

                db.commit()
            else:
                

            return {
                "message": "Store mapping updated successfully",
                "change_requests_updated": rows_affected,
            }

        except HTTPException as e:
            # Convert HTTPException to 500 status
            logger.error(f"HTTPException updating store mapping: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error updating store mapping: {str(e)}",
            )
        except SQLAlchemyError as e:
            # Convert SQLAlchemyError to descriptive HTTPException
            logger.error(f"Database error updating store mapping: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error updating store mapping: {str(e)}",
            )
        except Exception:
            # Re-raise other exceptions
            raise

    def update_store_mapping_metadata(
        self, db: Session, request_data: MappingUnmappingUpdateMetadataRequest
    ) -> MappingUnmappingUpdateMetadataResponse:
        """
        Update store mapping metadata fields in nutrostore.tsStoreMap table

        This method updates one or more metadata fields for a store mapping record
        identified by VCID. Only the fields provided in the request will be updated.
        Empty strings are converted to NULL values in the database.

        Args:
            db (Session): Database session
            request_data (MappingUnmappingUpdateMetadataRequest): Request containing VCID and fields to update

        Returns:
            MappingUnmappingUpdateMetadataResponse: Response with success status and updated fields

        Raises:
            HTTPException: 400 for validation errors, 404 if record not found, 500 for database errors
        """
        try:
            logger.info(
                f"Updating store mapping metadata for VCID: {request_data.VCID}"
            )

            # First, verify that the record exists in the table
            check_query = text(
                "SELECT COUNT(*) FROM nutrostore.tsStoreMap WHERE VCID = :VCID"
            )
            result = db.execute(check_query, {"VCID": request_data.VCID})
            record_count = result.scalar()

            # If no record found, raise 404 error
            if record_count == 0:
                logger.warning(f"No record found with VCID: {request_data.VCID}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Store mapping record with VCID {request_data.VCID} not found",
                )

            # Build dynamic update query based on provided fields
            # Only update fields that are explicitly provided (not None)
            update_fields = []
            update_params = {"VCID": request_data.VCID}
            updated_field_names = []

            # Check each field and add to update if provided
            # Convert empty strings to None (NULL in database)
            if request_data.customer_type is not None:
                customer_type_value = (
                    request_data.customer_type.strip()
                    if request_data.customer_type
                    else None
                )
                customer_type_value = (
                    customer_type_value if customer_type_value else None
                )
                update_fields.append("DistCustomerType = :customer_type")
                update_params["customer_type"] = customer_type_value
                updated_field_names.append("customer_type")

            if request_data.rep is not None:
                rep_value = request_data.rep.strip() if request_data.rep else None
                rep_value = rep_value if rep_value else None
                update_fields.append("DistRep = :rep")
                update_params["rep"] = rep_value
                updated_field_names.append("rep")

            if request_data.address is not None:
                address_value = (
                    request_data.address.strip() if request_data.address else None
                )
                address_value = address_value if address_value else None
                update_fields.append("DistStoreAddress = :address")
                update_params["address"] = address_value
                updated_field_names.append("address")

            if request_data.warehouse is not None:
                warehouse_value = (
                    request_data.warehouse.strip() if request_data.warehouse else None
                )
                warehouse_value = warehouse_value if warehouse_value else None
                update_fields.append("DistWarehouse = :warehouse")
                update_params["warehouse"] = warehouse_value
                updated_field_names.append("warehouse")

            if request_data.country is not None:
                country_value = (
                    request_data.country.strip() if request_data.country else None
                )
                country_value = country_value if country_value else None
                update_fields.append("DistStoreCountry = :country")
                update_params["country"] = country_value
                updated_field_names.append("country")

            if request_data.phone is not None:
                phone_value = request_data.phone.strip() if request_data.phone else None
                phone_value = phone_value if phone_value else None
                update_fields.append("DistStorePhone = :phone")
                update_params["phone"] = phone_value
                updated_field_names.append("phone")

            # If no fields to update, return validation error
            if not update_fields:
                logger.warning(
                    f"No fields provided for update for VCID: {request_data.VCID}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="At least one field must be provided for update",
                )

            # Build and execute the update query
            update_sql = f"""
                UPDATE nutrostore.tsStoreMap
                SET {", ".join(update_fields)}
                WHERE VCID = :VCID
            """

            logger.info(
                f"Executing update query for VCID {request_data.VCID} with fields: {updated_field_names}"
            )
            db.execute(text(update_sql), update_params)
            db.commit()

            # Return success response with details of what was updated
            return MappingUnmappingUpdateMetadataResponse(
                success=True,
                message="Store mapping metadata updated successfully",
                updated_fields=updated_field_names,
                VCID=request_data.VCID,
            )

        except HTTPException:
            # Re-raise HTTP exceptions (400, 404) without modification
            raise
        except Exception as e:
            # Log unexpected errors and raise 500 error
            logger.error(
                f"Unexpected error updating store mapping metadata for VCID {request_data.VCID}: {str(e)}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error updating store mapping metadata: {str(e)}",
            )

    def get_all_stores(
        self,
        db: Session,
        page: int = 1,
        limit: int = 100,
        search_query: Optional[str] = None,
        store_ids: Optional[List[int]] = None,
        store_names: Optional[List[str]] = None,
        chain_names: Optional[List[str]] = None,
        territory_managers: Optional[List[str]] = None,
        district_managers: Optional[List[str]] = None,
        store_numbers: Optional[List[str]] = None,
        account_types: Optional[List[str]] = None,
        report_categories: Optional[List[str]] = None,
    ) -> StoreListResponse:
        try:
            # Base query
            query = """
                SELECT 
                    NutroStoreID, StoreName, LocationAddress, LocationCity, 
                    LocationState, LocationZipCode, LocationCountry, Phone, 
                    ChainName, StoreNumber, StoreStatus, AccountType, 
                    ReportCategory, ReportCategoryDetail, Priority, 
                    TerritoryManager, DistrictManager, KAM
                FROM [activities].[tblnsimport]
                WHERE 1=1
            """

            # Add filters - using proper SQL Server parameter binding
            params = {}
            filter_conditions = []
            if search_query:
                search_terms = search_query.split()
                search_conditions = []
                for i, term in enumerate(search_terms):
                    term_conditions = []
                    term_params = {}

                    # Search across multiple fields with LIKE
                    fields_to_search = [
                        "StoreName",
                        "ChainName",
                        "StoreNumber",
                        "TerritoryManager",
                        "DistrictManager",
                        "LocationCity",
                        "LocationState",
                        "AccountType",
                        "ReportCategory",
                    ]

                    for j, field in enumerate(fields_to_search):
                        param_name = f"search_{i}_{j}"
                        term_conditions.append(f"{field} LIKE :{param_name}")
                        term_params[param_name] = f"%{term}%"

                    search_conditions.append(f"({' OR '.join(term_conditions)})")
                    params.update(term_params)

                filter_conditions.append(f"({' AND '.join(search_conditions)})")

            if store_ids:
                if len(store_ids) == 1:
                    filter_conditions.append("NutroStoreID = :store_id")
                    params["store_id"] = store_ids[0]
                else:
                    placeholders = ",".join(
                        [f":store_id_{i}" for i in range(len(store_ids))]
                    )
                    filter_conditions.append(f"NutroStoreID IN ({placeholders})")
                    for i, store_id in enumerate(store_ids):
                        params[f"store_id_{i}"] = store_id

            if store_names:
                if len(store_names) == 1:
                    filter_conditions.append("StoreName = :store_name")
                    params["store_name"] = store_names[0]
                else:
                    placeholders = ",".join(
                        [f":store_name_{i}" for i in range(len(store_names))]
                    )
                    filter_conditions.append(f"StoreName IN ({placeholders})")
                    for i, store_name in enumerate(store_names):
                        params[f"store_name_{i}"] = store_name

            # Apply similar pattern for other filters
            if chain_names:
                if len(chain_names) == 1:
                    filter_conditions.append("ChainName = :chain_name")
                    params["chain_name"] = chain_names[0]
                else:
                    placeholders = ",".join(
                        [f":chain_name_{i}" for i in range(len(chain_names))]
                    )
                    filter_conditions.append(f"ChainName IN ({placeholders})")
                    for i, chain_name in enumerate(chain_names):
                        params[f"chain_name_{i}"] = chain_name
            if territory_managers:
                if len(territory_managers) == 1:
                    filter_conditions.append("TerritoryManager = :territory_manager")
                    params["territory_manager"] = territory_managers[0]
                else:
                    placeholders = ",".join(
                        [
                            f":territory_manager_{i}"
                            for i in range(len(territory_managers))
                        ]
                    )
                    filter_conditions.append(f"TerritoryManager IN ({placeholders})")
                    for i, territory_manager in enumerate(territory_managers):
                        params[f"territory_manager_{i}"] = territory_manager
            if district_managers:
                if len(district_managers) == 1:
                    filter_conditions.append("DistrictManager = :district_manager")
                    params["district_manager"] = district_managers[0]
                else:
                    placeholders = ",".join(
                        [
                            f":district_manager_{i}"
                            for i in range(len(district_managers))
                        ]
                    )
                    filter_conditions.append(f"DistrictManager IN ({placeholders})")
                    for i, district_manager in enumerate(district_managers):
                        params[f"district_manager_{i}"] = district_manager
            if store_numbers:
                if len(store_numbers) == 1:
                    filter_conditions.append("StoreNumber = :store_number")
                    params["store_number"] = chain_names[0]
                else:
                    placeholders = ",".join(
                        [f":store_number_{i}" for i in range(len(store_numbers))]
                    )
                    filter_conditions.append(f"StoreNumber IN ({placeholders})")
                    for i, store_number in enumerate(store_numbers):
                        params[f"store_numbers_{i}"] = store_number
            if account_types:
                if len(account_types) == 1:
                    filter_conditions.append("AccountType = :account_type")
                    params["account_type"] = chain_names[0]
                else:
                    placeholders = ",".join(
                        [f":account_types_{i}" for i in range(len(account_types))]
                    )
                    filter_conditions.append(f"AccountType IN ({placeholders})")
                    for i, account_type in enumerate(account_types):
                        params[f"account_type_{i}"] = account_type
            if report_categories:
                if len(report_categories) == 1:
                    filter_conditions.append("ReportCategory = :report_categorie")
                    params["report_categorie"] = chain_names[0]
                else:
                    placeholders = ",".join(
                        [
                            f":report_categorie_{i}"
                            for i in range(len(report_categories))
                        ]
                    )
                    filter_conditions.append(f"ReportCategory IN ({placeholders})")
                    for i, report_categorie in enumerate(report_categories):
                        params[f"report_categorie_{i}"] = report_categorie

            # Add remaining filters with the same pattern...
            # [territory_managers, district_managers, store_numbers, account_types, report_categories]

            # Combine all filter conditions
            if filter_conditions:
                query += " AND " + " AND ".join(filter_conditions)

            # Count query for total records
            count_query = f"SELECT COUNT(*) FROM ({query}) AS subquery"
            total_count = db.execute(text(count_query), params).scalar()

            # Add pagination
            offset = (page - 1) * limit
            query += (
                " ORDER BY NutroStoreID OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY"
            )
            params["offset"] = offset
            params["limit"] = limit

            # Execute query
            result = db.execute(text(query), params)

            # Convert rows to StoreAttributionResponse objects
            store_responses = []
            for row in result:
                if hasattr(row, "_mapping"):  # SQLAlchemy 1.4+
                    row_data = dict(row._mapping)
                elif hasattr(row, "_asdict"):  # Older SQLAlchemy
                    row_data = row._asdict()
                else:  # Fallback
                    row_data = dict(zip(row.keys(), row))

                store_responses.append(StoreAttributionResponse(**row_data))

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return StoreListResponse(
                stores=store_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error fetching stores: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error fetching stores from database",
            )

    def get_stores_count(
        self,
        db: Session,
        search_query: Optional[str] = None,
        store_ids: Optional[List[int]] = None,
        store_names: Optional[List[str]] = None,
        chain_names: Optional[List[str]] = None,
        territory_managers: Optional[List[str]] = None,
        district_managers: Optional[List[str]] = None,
        store_numbers: Optional[List[str]] = None,
        account_types: Optional[List[str]] = None,
        report_categories: Optional[List[str]] = None,
    ) -> int:
        """
        Optimized count query for stores with filters
        """
        try:
            count_query = """
                SELECT COUNT(*) FROM [activities].[tblnsimport]
                WHERE 1=1
            """
            params = {}
            conditions = []
            if search_query:
                search_terms = search_query.split()
                search_conditions = []
                for i, term in enumerate(search_terms):
                    term_conditions = []
                    term_params = {}

                    fields_to_search = [
                        "StoreName",
                        "ChainName",
                        "StoreNumber",
                        "TerritoryManager",
                        "DistrictManager",
                        "LocationCity",
                        "LocationState",
                        "AccountType",
                        "ReportCategory",
                    ]

                    for j, field in enumerate(fields_to_search):
                        param_name = f"search_{i}_{j}"
                        term_conditions.append(f"{field} LIKE :{param_name}")
                        term_params[param_name] = f"%{term}%"

                    search_conditions.append(f"({' OR '.join(term_conditions)})")
                    params.update(term_params)

                conditions.append(f"({' AND '.join(search_conditions)})")

            # Add filter conditions
            conditions, params = self._add_filter_conditions(
                store_ids=store_ids,
                store_names=store_names,
                chain_names=chain_names,
                territory_managers=territory_managers,
                district_managers=district_managers,
                store_numbers=store_numbers,
                account_types=account_types,
                report_categories=report_categories,
                params=params,
            )

            if conditions:
                count_query += " AND " + " AND ".join(conditions)

            result = db.execute(text(count_query), params)
            return result.scalar()

        except SQLAlchemyError as e:
            logger.error(f"Database error counting stores: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error counting stores in database",
            )

    def get_all_stores_total(
        self,
        db: Session,
        search_query: Optional[str] = None,
        store_ids: Optional[List[int]] = None,
        store_names: Optional[List[str]] = None,
        chain_names: Optional[List[str]] = None,
        territory_managers: Optional[List[str]] = None,
        district_managers: Optional[List[str]] = None,
        store_numbers: Optional[List[str]] = None,
        account_types: Optional[List[str]] = None,
        report_categories: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: int = 0,
    ) -> StoreListTotalResponse:
        """
        Get filtered stores with pagination support for large exports
        """
        try:
            # Base query
            query = """
                SELECT 
                    NutroStoreID, StoreName, LocationAddress, LocationCity, 
                    LocationState, LocationZipCode, ChainName, StoreNumber, 
                    AccountType, TerritoryManager, DistrictManager
                FROM [activities].[tblnsimport]
                WHERE 1=1
            """

            params = {}
            conditions = []
            if search_query:
                search_terms = search_query.split()
                search_conditions = []
                for i, term in enumerate(search_terms):
                    term_conditions = []
                    term_params = {}

                    fields_to_search = [
                        "StoreName",
                        "ChainName",
                        "StoreNumber",
                        "TerritoryManager",
                        "DistrictManager",
                        "LocationCity",
                        "LocationState",
                        "AccountType",
                        "ReportCategory",
                    ]

                    for j, field in enumerate(fields_to_search):
                        param_name = f"search_{i}_{j}"
                        term_conditions.append(f"{field} LIKE :{param_name}")
                        term_params[param_name] = f"%{term}%"

                    search_conditions.append(f"({' OR '.join(term_conditions)})")
                    params.update(term_params)

                conditions.append(f"({' AND '.join(search_conditions)})")

            # Add filter conditions
            conditions, params = self._add_filter_conditions(
                store_ids=store_ids,
                store_names=store_names,
                chain_names=chain_names,
                territory_managers=territory_managers,
                district_managers=district_managers,
                store_numbers=store_numbers,
                account_types=account_types,
                report_categories=report_categories,
                params=params,
            )

            if conditions:
                query += " AND " + " AND ".join(conditions)

            # Add ordering for consistent pagination
            query += " ORDER BY NutroStoreID"

            # Add pagination if specified
            if limit is not None:
                query += " OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY"
                params["offset"] = offset
                params["limit"] = limit

            # Execute query
            result = db.execute(text(query), params)

            # Convert rows to StoreAttributionResponse objects
            stores = []
            for row in result:
                if hasattr(row, "_mapping"):  # SQLAlchemy 1.4+
                    row_data = dict(row._mapping)
                elif hasattr(row, "_asdict"):  # Older SQLAlchemy
                    row_data = row._asdict()
                else:  # Fallback
                    row_data = dict(zip(row.keys(), row))

                stores.append(StoreAttributionResponse(**row_data))

            # Get total count if we're paginating
            total_count = len(stores)
            if limit is None:
                total_count = self.get_stores_count(
                    db,
                    store_ids=store_ids,
                    store_names=store_names,
                    chain_names=chain_names,
                    territory_managers=territory_managers,
                    district_managers=district_managers,
                    store_numbers=store_numbers,
                    account_types=account_types,
                    report_categories=report_categories,
                )

            return StoreListTotalResponse(stores=stores, total_count=total_count)

        except SQLAlchemyError as e:
            logger.error(f"Database error fetching stores: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error fetching stores from database",
            )

    def _add_filter_conditions(
        self,
        store_ids: Optional[List[int]] = None,
        store_names: Optional[List[str]] = None,
        chain_names: Optional[List[str]] = None,
        territory_managers: Optional[List[str]] = None,
        district_managers: Optional[List[str]] = None,
        store_numbers: Optional[List[str]] = None,
        account_types: Optional[List[str]] = None,
        report_categories: Optional[List[str]] = None,
        params: Optional[dict] = None,
    ) -> Tuple[List[str], dict]:
        """
        Helper method to build filter conditions and parameters
        """
        if params is None:
            params = {}

        conditions = []

        if store_ids:
            if len(store_ids) == 1:
                conditions.append("NutroStoreID = :store_id")
                params["store_id"] = store_ids[0]
            else:
                placeholders = ",".join(
                    [f":store_id_{i}" for i in range(len(store_ids))]
                )
                conditions.append(f"NutroStoreID IN ({placeholders})")
                for i, store_id in enumerate(store_ids):
                    params[f"store_id_{i}"] = store_id

        if store_names:
            if len(store_names) == 1:
                conditions.append("StoreName = :store_name")
                params["store_name"] = store_names[0]
            else:
                placeholders = ",".join(
                    [f":store_name_{i}" for i in range(len(store_names))]
                )
                conditions.append(f"StoreName IN ({placeholders})")
                for i, store_name in enumerate(store_names):
                    params[f"store_name_{i}"] = store_name

        if chain_names:
            if len(chain_names) == 1:
                conditions.append("ChainName = :chain_name")
                params["chain_name"] = chain_names[0]
            else:
                placeholders = ",".join(
                    [f":chain_name_{i}" for i in range(len(chain_names))]
                )
                conditions.append(f"ChainName IN ({placeholders})")
                for i, chain_name in enumerate(chain_names):
                    params[f"chain_name_{i}"] = chain_name

        if territory_managers:
            if len(territory_managers) == 1:
                conditions.append("TerritoryManager = :territory_manager")
                params["territory_manager"] = territory_managers[0]
            else:
                placeholders = ",".join(
                    [f":territory_manager_{i}" for i in range(len(territory_managers))]
                )
                conditions.append(f"TerritoryManager IN ({placeholders})")
                for i, territory_manager in enumerate(territory_managers):
                    params[f"territory_manager_{i}"] = territory_manager

        if district_managers:
            if len(district_managers) == 1:
                conditions.append("DistrictManager = :district_manager")
                params["district_manager"] = district_managers[0]
            else:
                placeholders = ",".join(
                    [f":district_manager_{i}" for i in range(len(district_managers))]
                )
                conditions.append(f"DistrictManager IN ({placeholders})")
                for i, district_manager in enumerate(district_managers):
                    params[f"district_manager_{i}"] = district_manager

        if store_numbers:
            if len(store_numbers) == 1:
                conditions.append("StoreNumber = :store_number")
                params["store_number"] = store_numbers[0]
            else:
                placeholders = ",".join(
                    [f":store_number_{i}" for i in range(len(store_numbers))]
                )
                conditions.append(f"StoreNumber IN ({placeholders})")
                for i, store_number in enumerate(store_numbers):
                    params[f"store_number_{i}"] = store_number

        if account_types:
            if len(account_types) == 1:
                conditions.append("AccountType = :account_type")
                params["account_type"] = account_types[0]
            else:
                placeholders = ",".join(
                    [f":account_type_{i}" for i in range(len(account_types))]
                )
                conditions.append(f"AccountType IN ({placeholders})")
                for i, account_type in enumerate(account_types):
                    params[f"account_type_{i}"] = account_type

        if report_categories:
            if len(report_categories) == 1:
                conditions.append("ReportCategory = :report_category")
                params["report_category"] = report_categories[0]
            else:
                placeholders = ",".join(
                    [f":report_category_{i}" for i in range(len(report_categories))]
                )
                conditions.append(f"ReportCategory IN ({placeholders})")
                for i, report_category in enumerate(report_categories):
                    params[f"report_category_{i}"] = report_category

        return conditions, params

    async def fetch_store_filter_values(
        self,
        db: Session,
        field: str,
        search: Optional[str] = None,
        limit: int = 10,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """
        Fetch paginated filter values for a specific field with search capability

        Args:
            db: Database session
            field: Field name to query
            search: Optional search term
            limit: Number of items to return
            offset: Pagination offset

        Returns:
            Dictionary with values, total count, and pagination info
        """
        # Map field names to their corresponding column names
        field_to_column = {
            "store_ids": "NutroStoreID",
            "store_names": "StoreName",
            "chain_names": "ChainName",
            "territory_managers": "TerritoryManager",
            "district_managers": "DistrictManager",
            "store_numbers": "StoreNumber",
            "account_types": "AccountType",
            "report_categories": "ReportCategory",
        }

        column_name = field_to_column[field]
        base_query = f"""
            SELECT DISTINCT {column_name} 
            FROM [activities].[tblnsimport] 
            WHERE {column_name} IS NOT NULL
        """

        # Add search filter if provided
        if search:
            if field == "store_ids":
                # For numeric fields
                base_query += f" AND CAST({column_name} AS VARCHAR) LIKE :search"
            else:
                # For text fields
                base_query += f" AND {column_name} LIKE :search"

        # Get total count
        count_query = f"SELECT COUNT(*) FROM ({base_query}) AS subquery"
        total_count = db.execute(
            text(count_query), {"search": f"%{search}%"} if search else {}
        ).scalar()

        # Add ordering and pagination
        paginated_query = f"""
            SELECT * FROM ({base_query}) AS subquery
            ORDER BY {column_name}
            OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
        """

        # Execute the query
        rows = db.execute(
            text(paginated_query),
            {"search": f"%{search}%", "offset": offset, "limit": limit}
            if search
            else {"offset": offset, "limit": limit},
        ).fetchall()

        values = [row[0] for row in rows]

        return {
            "field": field,
            "values": values,
            "total": total_count,
            "limit": limit,
            "offset": offset,
        }

    def update_store(
        self, db: Session, nutro_store_id: int, update_data: StoreUpdateRequest
    ) -> StoreAttributionResponse:
        """
        Update store record by NutroStoreID

        Args:
            db: Database session
            nutro_store_id: ID of the store to update
            update_data: Fields to update

        Returns:
            StoreAttributionResponse: Updated store record
        """
        try:
            # Check if store exists
            check_query = """
                SELECT 1 FROM [activities].[tblnsimport] 
                WHERE NutroStoreID = :nutro_store_id
            """
            exists = db.execute(
                text(check_query), {"nutro_store_id": nutro_store_id}
            ).scalar()

            if not exists:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Store with ID {nutro_store_id} not found",
                )

            # Build update query
            update_fields = []
            params = {"nutro_store_id": nutro_store_id}

            for field, value in update_data.model_dump(exclude_unset=True).items():
                if value is not None:
                    update_fields.append(f"{field} = :{field}")
                    params[field] = value

            if not update_fields:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No fields provided for update",
                )

            update_query = f"""
                UPDATE [activities].[tblnsimport] 
                SET {", ".join(update_fields)}
                WHERE NutroStoreID = :nutro_store_id
            """

            db.execute(text(update_query), params)
            db.commit()

            # Return updated record
            return self.get_store_by_id(db, nutro_store_id)

        except HTTPException:
            raise
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error updating store: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating store in database",
            )

    def delete_stores(
        self, db: Session, delete_request: DeleteRequest
    ) -> Dict[str, Any]:
        """
        Delete multiple stores by NutroStoreID

        Args:
            db: Database session
            delete_request: List of NutroStoreIDs to delete

        Returns:
            Dict with deletion summary
        """
        try:
            if not delete_request.nutro_store_ids:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No store IDs provided for deletion",
                )

            # Convert list to comma-separated string for SQL IN clause
            id_list = ",".join(str(id) for id in delete_request.nutro_store_ids)

            # First check which IDs exist
            check_query = f"""
                SELECT NutroStoreID FROM [activities].[tblnsimport] 
                WHERE NutroStoreID IN ({id_list})
            """
            existing_ids = [row[0] for row in db.execute(text(check_query)).fetchall()]

            missing_ids = set(delete_request.nutro_store_ids) - set(existing_ids)

            if missing_ids:
                logger.warning(f"Attempt to delete non-existent stores: {missing_ids}")

            # Delete only existing records
            if existing_ids:
                existing_id_list = ",".join(str(id) for id in existing_ids)
                delete_query = f"""
                    DELETE FROM [activities].[tblnsimport] 
                    WHERE NutroStoreID IN ({existing_id_list})
                """
                db.execute(text(delete_query))
                db.commit()

            return {
                "message": "Deletion completed",
                "deleted_count": len(existing_ids),
                "not_found_ids": list(missing_ids),
            }

        except HTTPException:
            raise
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error deleting stores: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting stores from database",
            )

    def get_store_by_id(
        self, db: Session, nutro_store_id: int
    ) -> StoreAttributionResponse:
        try:
            query = """
                SELECT 
                    NutroStoreID, StoreName, LocationAddress, LocationCity, 
                    LocationState, LocationZipCode, LocationCountry, Phone, 
                    ChainName, StoreNumber, StoreStatus, AccountType, 
                    ReportCategory, ReportCategoryDetail, Priority, 
                    TerritoryManager, DistrictManager, KAM
                FROM [activities].[tblnsimport] 
                WHERE NutroStoreID = :nutro_store_id
            """

            result = db.execute(text(query), {"nutro_store_id": nutro_store_id})
            store = (
                result.mappings().first()
            )  # Changed from fetchone() to mappings().first()

            if not store:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Store with ID {nutro_store_id} not found",
                )

            return StoreAttributionResponse(**store)

        except HTTPException:
            raise
        except SQLAlchemyError as e:
            logger.error(f"Database error fetching store: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error fetching store from database",
            )

    def find_stores(
        self,
        db: Session,
        nutro_store_id: Optional[str] = None,
        chain_name: Optional[str] = None,
        store_name: Optional[str] = None,
        store_number: Optional[str] = None,
        address: Optional[str] = None,
        city: Optional[str] = None,
        state: Optional[str] = None,
        zip_code: Optional[str] = None,
        phone: Optional[str] = None,
        page: Optional[int] = None,
        limit: Optional[int] = None,
    ) -> FindStoreResponse:
        """
        Search stores using stored procedure with pagination

        Args:
            db: Database session
            nutro_store_id: Optional Nutro Store ID filter
            chain_name: Optional Chain name filter
            store_name: Optional Store name filter
            store_number: Optional Store number filter
            address: Optional Address filter
            city: Optional City filter
            state: Optional State filter
            zip_code: Optional Zip code filter
            phone: Optional Phone filter
            page: Optional page number (1-based)
            limit: Optional number of records per page

        Returns:
            StoreSearchResponse: Store search results with pagination metadata

        Raises:
            HTTPException: 500 for database errors
        """
        try:
            logger.info(
                f"Searching stores with filters: NutroStoreID={nutro_store_id}, "
                f"ChainName={chain_name}, StoreName={store_name}, "
                f"StoreNumber={store_number}, Address={address}, "
                f"City={city}, State={state}, ZipCode={zip_code}, Phone={phone}"
            )

            # Execute the stored procedure to search stores
            sql = text("""
                    EXEC [nutrostore].[store_mapping_StoreSearch] 
                        @NutroStoreID = :nutro_store_id,
                        @ChainName = :chain_name,
                        @StoreName = :store_name,
                        @StoreNumber = :store_number,
                        @Address = :address,
                        @City = :city,
                        @State = :state,
                        @ZipCode = :zip_code,
                        @Phone = :phone
                """)

            result = db.execute(
                sql,
                {
                    "nutro_store_id": nutro_store_id,
                    "chain_name": chain_name,
                    "store_name": store_name,
                    "store_number": store_number,
                    "address": address,
                    "city": city,
                    "state": state,
                    "zip_code": zip_code,
                    "phone": phone,
                },
            )
            rows = result.fetchall()

            # Transform all database rows to store search results
            all_results = [self._transform_store_search_result(row) for row in rows]

            logger.info(f"Found {len(all_results)} store record(s)")

            # Apply pagination if specified
            paginated_results, pagination_meta = self._apply_pagination(
                all_results, page, limit
            )

            # Return response with pagination metadata
            return FindStoreResponse(
                results=paginated_results,
                total_count=len(all_results),
                page=pagination_meta.get("page"),
                limit=pagination_meta.get("limit"),
                has_next=pagination_meta.get("has_next", False),
                has_previous=pagination_meta.get("has_previous", False),
            )

        except Exception as e:
            logger.error(f"Error searching stores: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching stores: {str(e)}",
            )

    def _transform_store_search_result(self, row) -> Dict[str, Any]:
        """
        Transform database row to store search result dictionary format

        Args:
            row: Database result row from stored procedure

        Returns:
            Dict: Transformed store search result dictionary
        """
        try:
            return {
                "NutroStoreID": getattr(row, "NutroStoreID", None),
                "ChainName": getattr(row, "ChainName", None),
                "StoreName": getattr(row, "StoreName", None),
                "StoreNumber": getattr(row, "StoreNumber", None),
                "Address": getattr(row, "Address", None),
                "City": getattr(row, "City", None),
                "State": getattr(row, "State", None),
                "ZipCode": getattr(row, "ZipCode", None),
                "Phone": getattr(row, "Phone", None),
            }
        except Exception as e:
            logger.error(f"Error transforming store search result: {str(e)}")
            return {
                "NutroStoreID": None,
                "ChainName": None,
                "StoreName": None,
                "StoreNumber": None,
                "Address": None,
                "City": None,
                "State": None,
                "ZipCode": None,
                "Phone": None,
            }

    def _apply_pagination(
        self, items: List[Any], page: Optional[int], limit: Optional[int]
    ) -> tuple:
        """
        Apply pagination to a list of items

        Args:
            items: List of items to paginate
            page: Page number (1-based)
            limit: Number of items per page

        Returns:
            tuple: (paginated_items, pagination_metadata)
        """
        if page is None or limit is None:
            return items, {
                "page": None,
                "limit": None,
                "has_next": False,
                "has_previous": False,
            }

        total_items = len(items)
        total_pages = (total_items + limit - 1) // limit
        has_next = page < total_pages
        has_previous = page > 1

        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_items = items[start_idx:end_idx]

        return paginated_items, {
            "page": page,
            "limit": limit,
            "has_next": has_next,
            "has_previous": has_previous,
        }
