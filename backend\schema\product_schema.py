from pydantic import BaseModel, Field, field_serializer
from typing import List, Optional
from datetime import date, datetime


class ProductResponse(BaseModel):
    """Schema for product search response with required fields"""

    PRDNO: str = Field(..., description="Product number")
    DESCP: str = Field(..., description="Product description")
    ItemID: int = Field(..., description="Item ID")
    SkuName: str = Field(..., description="SKU name")
    AssociatedItem: str = Field(..., description="Associated item")

    # Optional fields
    BrandName: Optional[str] = None
    REUPC: Optional[str] = None
    GRUPC: Optional[str] = None
    # Add other optional fields as needed from the vsProduct view

    class Config:
        extra = "ignore"  # Ignore any extra fields that might come from the DB


class ProductMappingResponse(BaseModel):
    PRDNO: str
    FromPRDNO: Optional[str]
    ToPRDNO: Optional[str]
    DataSource: Optional[str]
    LastTransactionDate: Optional[date]  # Changed to date type
    L4POSSales: Optional[float]
    IsPending: Optional[bool]
    MappingStatus: str

    @field_serializer("LastTransactionDate")
    def serialize_date(self, dt: Optional[date], _info):
        if dt is None:
            return None
        return dt.isoformat()  # Convert date to ISO format string


class ProductSearchResponse(BaseModel):
    """Schema for paginated product search response"""

    products: List[ProductResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_previous: bool


class ProductExtensionResponse(BaseModel):
    Recid: int
    Prdno: str
    Property: str
    Value: str


class ProductMappingResponseList(BaseModel):
    """Schema for paginated product mapping response"""

    mappings: List[ProductMappingResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_previous: bool


class ProductExtensionResponseList(BaseModel):
    """Schema for paginated product extension response"""

    extensions: List[ProductExtensionResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_previous: bool


class ListItemResponse(BaseModel):
    RecID: Optional[int]
    ListName: Optional[str]
    ItemName: Optional[str]
    ItemValue: Optional[str]
    ItemDescription: Optional[str]
    ItemOrder: Optional[int]
    ParentListName: Optional[str]
    ParentItemValue: Optional[str]
    CreatedDate: Optional[datetime]


class PriceListResponse(BaseModel):
    PriceList: str


class CustomerNameResponse(BaseModel):
    CNAME: str
    CUSNO: str


class ProductDetailsResponse(BaseModel):
    # Add all fields from vsProduct view as optional
    PRDNO: str
    BrandName: Optional[str]
    SkuName: Optional[str]
    DESCP: Optional[str]
    REUPC: Optional[str]
    GRUPC: Optional[str]
    AssociatedItem: Optional[str]
    # Add other fields as needed...


class ProductBasicResponse(BaseModel):
    # Same as ProductDetailsResponse but with fewer required fields
    PRDNO: str
    BrandName: Optional[str]
    SkuName: Optional[str]
    DESCP: Optional[str]
    # Add other fields as needed...
