from fastapi import APIRouter

# from api.v1.endpoints import login_api
from api.v1.endpoints import attribute_list_api
from api.v1.endpoints import user_api
from api.v1.endpoints import custom_upload
from api.v1.endpoints import store_api
from api.v1.endpoints import product_api
# from msal import msal_auth

app_router = APIRouter()

# app_router.include_router(login_api.router, tags=['Login'])
# app_router.include_router(msal_auth.router, prefix='', tags=['Login'])
app_router.include_router(
    attribute_list_api.router, prefix="/attribute_list", tags=["Attribute List"]
)
app_router.include_router(user_api.router, prefix="/users", tags=["Users"])
app_router.include_router(
    custom_upload.router, prefix="/custom_upload", tags=["Custom Upload Apis"]
)
app_router.include_router(store_api.router, prefix="/stores", tags=["Stores"])
app_router.include_router(product_api.router, prefix="/products", tags=["Products"])
