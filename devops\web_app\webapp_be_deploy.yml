trigger:
  branches:
    include:
      # - main
      - npddc/dev
  paths:
    include:
      - backend/*

variables:
  - group: PNDEM-NPDDC-WEBAPP-BE


stages:

  # - stage: Sonarqube_Analysis
  #   jobs:      
  #     - template: ../pipelines/jobs/sonarqube/run-and-publish-analysis-job.yml
  #       parameters:
  #         sonarqubeInstance: pndem_npddc_be
  #         projectKey: pndem-npddc-be
  #         projectName: pndem_npddc_be

  - stage: Dev_Deplyoment
    pool:
      name: "DNA-Selfhosted-dev-eastus2-aks-pool"
    jobs:
      - template: ../pipelines/jobs/web_app/build-and-push-job.yml
        parameters:
          environmentName: PNDEM_NPDDC_DEV
          registryServiceConnection: pndempdapeus2devconreg
          registryUri: pndempdapeus2devcr.azurecr.io
          imageName: "npddcbe"
          tags: latest
          dockerfilePath: $(Build.SourcesDirectory)/backend/Dockerfile # Example: "$(Build.SourcesDirectory)/Dockerfile"
          azureSubscription: PNDEM-NPDDC-DEV # BEST PRACTICE: reference to variable from defined variable group
          appName: npddcbeeus2devas
          appSettings: "-WEBSITE_PULL_IMAGE_OVER_VNET 1 -WEBSITES_PORT 8000" # BEST PRACTICE: Add "-WEBSITE_PULL_IMAGE_OVER_VNET 1"
          appConfiguration: ""
          arguments: --build-arg DB_SERVER="$(DB_SERVER)" --build-arg DB_NAME="$(DB_NAME)" --build-arg DB_USERNAME="$(DB_USERNAME)" --build-arg DB_PASSWORD="$(DB_PASSWORD)" --build-arg DB_DRIVER="$(DB_DRIVER)" --build-arg TENANT_ID="$(TENANT_ID)" --build-arg AZURE_CLIENT_ID="$(AZURE_CLIENT_ID)" --build-arg AZURE_CLIENT_SECRET="$(AZURE_CLIENT_SECRET)" --build-arg AZURE_STORAGE_ACCOUNT_URL="$(AZURE_STORAGE_ACCOUNT_URL)" --build-arg AZURE_STORAGE_CONTAINER_NAME="$(AZURE_STORAGE_CONTAINER_NAME)"
 
  # - stage: PROD
  #   pool:
  #     name: <Name of a self hosted runner for PROD>
  #   jobs:
  #     - template: jobs/web_app/build-and-push-job.yml@templates
  #       parameters:
  #         environmentName: prpd
  #         registryServiceConnection: <registry_service_connection>
  #         registryUri: <registry_server_uri>
  #         imageName: <image_name>
  #         tags: <image_tag>
  #         dockerfilePath: <path_to_dockerfile> # Example: "$(Build.SourcesDirectory)/Dockerfile"
  #         azureSubscription: <azure_subscription> # BEST PRACTICE: reference to variable from defined variable group
  #         appName: <app_name>
  #         appSettings: <app_settings> # BEST PRACTICE: Add "-WEBSITE_PULL_IMAGE_OVER_VNET 1"
  #         appConfiguration: <app_configuration>
  #         arguments: <docker_image_build_arguments> #for example --build-arg DEBUG="$(DEBUG)" --build-arg IS_TESTING="$(IS_TESTING)" 
