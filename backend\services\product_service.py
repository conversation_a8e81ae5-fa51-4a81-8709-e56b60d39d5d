from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException, status
import logging

from schema.product_schema import (
    ProductExtensionResponseList,
    ProductMappingResponse,
    ProductMappingResponseList,
    ProductSearchResponse,
    ProductResponse,
    ListItemResponse,
)

logger = logging.getLogger(__name__)


class ProductService:
    """Service class for product-related operations"""

    def search_products(
        self,
        db: Session,
        BrandName: Optional[str] = None,
        SkuName: Optional[str] = None,
        PRMSDescription: Optional[str] = None,
        RetailUPC: Optional[str] = None,
        PRDNO: Optional[str] = None,
        AssociatedItem: Optional[str] = None,
        page: int = 1,
        limit: int = 100,
    ) -> ProductSearchResponse:
        """
        Search products using the product_Search stored procedure with pagination

        Args:
            db: Database session
            BrandName: Brand name to search
            SkuName: SKU name to search
            PRMSDescription: PRMS description to search
            RetailUPC: Retail UPC to search
            PRDNO: Product number to search
            AssociatedItem: Associated item to search
            page: Page number (1-based)
            limit: Number of records per page

        Returns:
            ProductSearchResponse: Paginated search results
        """
        try:
            # Prepare parameters - convert empty strings to None
            params = {
                "BrandName": BrandName if BrandName else None,
                "SkuName": SkuName if SkuName else None,
                "PRMSDescription": PRMSDescription if PRMSDescription else None,
                "RetailUPC": RetailUPC if RetailUPC else None,
                "PRDNO": PRDNO if PRDNO else None,
                "AssociatedItem": AssociatedItem if AssociatedItem else None,
            }

            # Calculate offset
            offset = (page - 1) * limit

            # Execute the stored procedure
            sql = text("""
                EXEC [nutrostore].[product_Search]
                    @BrandName = :BrandName,
                    @SkuName = :SkuName,
                    @PRMSDescription = :PRMSDescription,
                    @RetailUPC = :RetailUPC,
                    @PRDNO = :PRDNO,
                    @AssociatedItem = :AssociatedItem
            """)

            result = db.execute(sql, params)
            all_products = result.fetchall()

            # Apply pagination in Python
            products = all_products[offset : offset + limit]
            total_count = len(all_products)

            # Convert to response objects
            product_responses = [
                ProductResponse(**self._row_to_dict(row)) for row in products
            ]

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return ProductSearchResponse(
                products=product_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error executing product_Search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error executing stored procedure: {str(e)}",
            )
        except Exception as e:
            logger.error(f"Unexpected error executing product_Search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error executing stored procedure: {str(e)}",
            )

    def get_product_mappings(
        self,
        db: Session,
        prdno: str,
        page: int = 1,
        limit: int = 100,
    ) -> ProductMappingResponseList:
        """Execute product_mapping_SelectByPRDNO stored procedure with pagination"""
        try:
            sql = text(
                "EXEC [Nutrostore].[product_mapping_SelectByPRDNO] @PRDNO = :prdno"
            )
            result = db.execute(sql, {"prdno": prdno})

            all_mappings = result.fetchall()

            # Apply pagination
            offset = (page - 1) * limit
            mappings = all_mappings[offset : offset + limit]
            total_count = len(all_mappings)

            # Convert to response objects
            mapping_responses = []
            for row in mappings:
                row_dict = {
                    "PRDNO": row.PRDNO,
                    "FromPRDNO": row.FromPRDNO,
                    "ToPRDNO": row.ToPRDNO,
                    "DataSource": row.DataSource,
                    "LastTransactionDate": row.LastTransactionDate,
                    "L4POSSales": row.L4POSSales,
                    "IsPending": row.IsPending,
                    "MappingStatus": row.MappingStatus,
                }
                mapping_responses.append(ProductMappingResponse(**row_dict))

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return ProductMappingResponseList(
                mappings=mapping_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except SQLAlchemyError as e:
            logger.error(f"Error getting product mappings: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Database error getting product mappings"
            )

    def get_product_extensions(
        self,
        db: Session,
        prdno: str,
        page: int = 1,
        limit: int = 100,
    ) -> ProductExtensionResponseList:
        """Execute product_extension_get stored procedure with pagination"""
        try:
            sql = text("EXEC [nutrostore].[product_extension_get] @prdno = :prdno")
            result = db.execute(sql, {"prdno": prdno})

            all_extensions = result.fetchall()

            # Apply pagination
            offset = (page - 1) * limit
            extensions = all_extensions[offset : offset + limit]
            total_count = len(all_extensions)

            # Convert to response objects
            extension_responses = [
                {
                    "Recid": row.Recid,
                    "Prdno": row.Prdno,
                    "Property": row.Property,
                    "Value": row.Value,
                }
                for row in extensions
            ]

            # Calculate pagination metadata
            has_next = (offset + limit) < total_count
            has_previous = page > 1

            return ProductExtensionResponseList(
                extensions=extension_responses,
                total_count=total_count,
                page=page,
                limit=limit,
                has_next=has_next,
                has_previous=has_previous,
            )

        except SQLAlchemyError as e:
            logger.error(f"Error getting product extensions: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Database error getting product extensions"
            )

    def get_distinct_list_names(self, db: Session) -> List[str]:
        """
        Get distinct list names by executing a modified version of the stored procedure
        """
        try:
            # Execute modified stored procedure call
            sql = text("""
                SELECT DISTINCT ListName 
                FROM [nutrostore].[tlListItems]
                ORDER BY ListName
            """)
            result = db.execute(sql)
            return [row.ListName for row in result.fetchall()]

        except SQLAlchemyError as e:
            logger.error(f"Database error getting distinct list names: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Database error retrieving list names"
            )

    def get_list_items(
        self,
        db: Session,
        ListName: str,
        ParentListName: Optional[str] = None,
        ParentItemValue: Optional[str] = None,
    ) -> List[ListItemResponse]:
        """
        Execute the nlp_list_items_Select stored procedure
        """
        try:
            # Execute the stored procedure exactly as defined
            sql = text("""
                EXEC [nutrostore].[nlp_list_items_Select]
                    @ListName = :ListName,
                    @ParentListName = :ParentListName,
                    @ParentItemValue = :ParentItemValue
            """)

            params = {
                "ListName": ListName,
                "ParentListName": ParentListName,
                "ParentItemValue": ParentItemValue,
            }

            result = db.execute(sql, params)
            items = result.fetchall()

            return [
                ListItemResponse(
                    RecID=item.RecID,
                    ListName=item.ListName,
                    ItemName=item.ItemName,
                    ItemValue=item.ItemValue,
                    ItemDescription=item.ItemDescription,
                    ItemOrder=item.ItemOrder,
                    ParentListName=item.ParentListName,
                    ParentItemValue=item.ParentItemValue,
                    CreatedDate=item.CreatedDate,
                )
                for item in items
            ]

        except SQLAlchemyError as e:
            logger.error(f"Database error executing nlp_list_items_Select: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Database error executing stored procedure"
            )

    def _row_to_dict(self, row) -> Dict[str, Any]:
        """
        Convert product search row to dictionary with required fields

        Args:
            row: Database result row

        Returns:
            Dictionary representation of the row with required fields
        """
        # First get all available attributes from the row
        row_dict = {col: getattr(row, col, None) for col in row._fields}

        # Ensure required fields have values (use empty string if None)
        required_fields = {
            "PRDNO": row_dict.get("PRDNO") or "",
            "DESCP": row_dict.get("DESCP") or "",
            "ItemID": row_dict.get("ItemID") or "",
            "SkuName": row_dict.get("SkuName") or "",
            "AssociatedItem": row_dict.get("AssociatedItem") or "",
        }

        # Add optional fields
        optional_fields = {
            "BrandName": row_dict.get("BrandName"),
            "REUPC": row_dict.get("REUPC"),
            "GRUPC": row_dict.get("GRUPC"),
            # Add other optional fields as needed
        }

        # Combine required and optional fields
        return {**required_fields, **optional_fields}

    def _list_item_to_dict(self, row) -> Dict[str, Any]:
        """
        Convert list item row to dictionary

        Args:
            row: Database result row

        Returns:
            Dictionary representation of the row
        """
        return {
            "RecID": getattr(row, "RecID", None),
            "ListName": getattr(row, "ListName", None),
            "ItemName": getattr(row, "ItemName", None),
            "ItemValue": getattr(row, "ItemValue", None),
            "ItemDescription": getattr(row, "ItemDescription", None),
            "ItemOrder": getattr(row, "ItemOrder", None),
            "ParentListName": getattr(row, "ParentListName", None),
            "ParentItemValue": getattr(row, "ParentItemValue", None),
            "CreatedDate": getattr(row, "CreatedDate", None),
        }

    def get_price_lists(self, db: Session, prdno: str) -> List[str]:
        """Execute product_Select_PriceList_List stored procedure"""
        try:
            sql = text(
                "EXEC [nutrostore].[product_Select_PriceList_List] @PRDNO = :prdno"
            )
            result = db.execute(sql, {"prdno": prdno})
            return [row.PriceList for row in result.fetchall()]
        except SQLAlchemyError as e:
            logger.error(f"Error getting price lists: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Database error getting price lists"
            )

    def get_customer_names(self, db: Session, prdno: str) -> List[dict]:
        """Execute product_Select_PRMS_CNAME_List stored procedure"""
        try:
            sql = text(
                "EXEC [nutrostore].[product_Select_PRMS_CNAME_List] @PRDNO = :prdno"
            )
            result = db.execute(sql, {"prdno": prdno})
            return [
                {"CNAME": row.CNAME, "CUSNO": row.CUSNO} for row in result.fetchall()
            ]
        except SQLAlchemyError as e:
            logger.error(f"Error getting customer names: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Database error getting customer names"
            )

    def get_product_details(self, db: Session, prdno: str) -> dict:
        """Execute product_Select_ProductDetails stored procedure"""
        try:
            sql = text("EXEC [nutrostore].[product_Select_ProductDetails] @ID = :prdno")
            result = db.execute(sql, {"prdno": prdno})

            # Get the first row (should only be one for a single PRDNO)
            row = result.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Product not found")

            # Properly convert SQLAlchemy row to dictionary
            return {column: getattr(row, column) for column in row._fields}

        except SQLAlchemyError as e:
            logger.error(f"Error getting product details: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Database error getting product details"
            )

    def get_product_basic(self, db: Session, prdno: str) -> dict:
        """Execute product_Select stored procedure"""
        try:
            sql = text("EXEC [Nutrostore].[product_Select] @PRDNO = :prdno")
            result = db.execute(sql, {"prdno": prdno})

            # Get the first row (should only be one for a single PRDNO)
            row = result.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Product not found")

            # Convert row to dictionary properly
            return {column: getattr(row, column) for column in row._fields}

        except SQLAlchemyError as e:
            logger.error(f"Error getting basic product info: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Database error getting basic product info"
            )
