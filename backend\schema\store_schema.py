from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from decimal import Decimal
from datetime import date


class StoreSearchRequest(BaseModel):
    """Schema for store search request parameters"""

    NutroStoreID: Optional[str] = Field(default=None, max_length=50)
    ChainName: Optional[str] = Field(default=None, max_length=50)
    StoreName: Optional[str] = Field(default=None, max_length=50)
    StoreNumber: Optional[str] = Field(default=None, max_length=50)
    Address: Optional[str] = Field(default=None, max_length=50)
    City: Optional[str] = Field(default=None, max_length=50)
    State: Optional[str] = Field(default=None, max_length=50)
    ZipCode: Optional[str] = Field(default=None, max_length=50)
    Phone: Optional[str] = Field(default=None, max_length=50)
    TerritoryDivID: Optional[str] = Field(default=None, max_length=10)


class StoreResponse(BaseModel):
    """Schema for store search response"""

    NutroStoreID: Optional[int]
    ChainName: Optional[str]
    StoreName: Optional[str]
    Address: Optional[str]
    City: Optional[str]
    State: Optional[str]
    ZipCode: Optional[str]
    Phone: Optional[str]
    TerritoryManager: Optional[str]
    Status: Optional[str]

    class Config:
        from_attributes = True


class StoreSearchResponse(BaseModel):
    """Schema for paginated store search response"""

    stores: List[StoreResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_previous: bool

    class Config:
        from_attributes = True


class NutroStoreDetail(BaseModel):
    """Schema for individual nutro store detail record"""

    DataSource: Optional[str] = Field(
        default=None, description="Data source information"
    )
    DistStoreID: Optional[str] = Field(
        default=None, description="Distribution store ID"
    )
    DistStoreName: Optional[str] = Field(
        default=None, description="Distribution store name/description"
    )
    DistStoreLocation: Optional[str] = Field(
        default=None, description="Combined city, state, zip"
    )
    LastTransactionDate: Optional[str] = Field(
        default=None, description="Last transaction date in MM/DD/YYYY format"
    )
    L52POSSales: Optional[Decimal] = Field(
        default=None, description="POS sales since 5/2/25"
    )
    L4POSSales: Optional[Decimal] = Field(
        default=None, description="POS sales since 5/31/24"
    )
    TotalPOSSales: Optional[Decimal] = Field(
        default=None, description="Total POS sales since 1/1/10"
    )
    MappingStatus: Optional[str] = Field(
        default=None, description="Current mapping status"
    )
    VCID: Optional[int] = Field(default=None, description="VCID (Mapping ID)")

    class Config:
        from_attributes = True


class NutroStoreDetailResponse(BaseModel):
    """Schema for nutro store detail response with multiple records and pagination"""

    stores: List[NutroStoreDetail] = Field(description="List of store detail records")
    total_count: int = Field(description="Total number of records (before pagination)")
    page: Optional[int] = Field(
        default=None, description="Current page number (if pagination used)"
    )
    limit: Optional[int] = Field(
        default=None, description="Records per page (if pagination used)"
    )
    has_next: bool = Field(default=False, description="Whether there are more pages")
    has_previous: bool = Field(
        default=False, description="Whether there are previous pages"
    )

    class Config:
        from_attributes = True


class NutroStoreMetaData(BaseModel):
    """Schema for individual nutro store metadata record"""

    Address: Optional[str] = Field(
        default=None,
        description="Complete store address (LocationAddress, LocationCity, MailingState MailingZipCode)",
    )
    Phone: Optional[str] = Field(default=None, description="Store phone number")
    Contact: Optional[str] = Field(default=None, description="Store contact person")
    Chain: Optional[str] = Field(default=None, description="Chain name")
    AccountType: Optional[str] = Field(default=None, description="Account type")
    CategoryLevelOne: Optional[str] = Field(
        default=None, description="Category level one (Account type detail)"
    )
    CategoryLevelTwo: Optional[str] = Field(
        default=None, description="Category level two (Report category)"
    )
    CategoryLevelThree: Optional[str] = Field(
        default=None, description="Category level three (Report category detail)"
    )
    Status: Optional[str] = Field(default=None, description="Store status description")
    TM: Optional[str] = Field(default=None, description="Territory Manager")

    class Config:
        from_attributes = True


class NutroStoreMetaDataResponse(BaseModel):
    """Schema for nutro store metadata response with multiple records"""

    stores: List[NutroStoreMetaData] = Field(
        description="List of store metadata records"
    )
    total_count: int = Field(description="Total number of records returned")

    class Config:
        from_attributes = True


class ChangeLogEntry(BaseModel):
    """Schema for individual change log entry"""

    ChangeDate: str = Field(
        description="Change date in MM/DD/YYYY H:MM:SS AM/PM format"
    )
    LastModifiedBy: Optional[str] = Field(
        default=None, description="User who made the change"
    )
    ChangeType: Optional[str] = Field(default=None, description="Type of change made")

    class Config:
        from_attributes = True


class ChangeLogResponse(BaseModel):
    """Schema for change log response with pagination"""

    changes: List[ChangeLogEntry] = Field(description="List of change log entries")
    total_count: int = Field(description="Total number of records returned")
    page: Optional[int] = Field(default=None, description="Current page number")
    limit: Optional[int] = Field(default=None, description="Number of records per page")
    has_next: bool = Field(default=False, description="Whether there are more pages")
    has_previous: bool = Field(
        default=False, description="Whether there are previous pages"
    )

    class Config:
        from_attributes = True


class MappingDetail(BaseModel):
    """Schema for individual mapping detail record"""

    DataSource: Optional[str] = Field(
        default=None, description="Data source information"
    )
    DataStoreName: Optional[str] = Field(default=None, description="Data store name")
    DataStoreID: Optional[str] = Field(default=None, description="Data store ID")
    Address: Optional[str] = Field(
        default=None,
        description="Complete address (DistStoreAddress, DistStoreCity, DistStoreState, DistStoreZip)",
    )
    CustomerType: Optional[str] = Field(default=None, description="Customer type")
    Warehouse: Optional[str] = Field(default=None, description="Warehouse information")
    Rep: Optional[str] = Field(default=None, description="Representative")
    Country: Optional[str] = Field(default=None, description="Store country")
    FirstTransDate: Optional[str] = Field(
        default=None, description="First transaction date in MM/DD/YYYY format"
    )
    LastTransDate: Optional[str] = Field(
        default=None, description="Last transaction date in MM/DD/YYYY format"
    )
    Last4WeekPOS: Optional[Decimal] = Field(
        default=None, description="Last 4 week POS sales"
    )
    Last52WeekPOS: Optional[Decimal] = Field(
        default=None, description="Last 52 week POS sales"
    )
    Total2010On: Optional[Decimal] = Field(
        default=None, description="Total POS sales from 2010 onwards"
    )
    Phone: Optional[str] = Field(default=None, description="Store phone number")
    MappingStatus: Optional[str] = Field(default=None, description="Mapping status")
    IsPending: Optional[bool] = Field(
        default=None, description="Whether the mapping is pending"
    )

    class Config:
        from_attributes = True


class MappingDetailResponse(BaseModel):
    """Schema for mapping detail response"""

    mappings: List[MappingDetail] = Field(description="List of mapping detail records")
    total_count: int = Field(description="Total number of records returned")

    class Config:
        from_attributes = True


class DistStoreSearchRequest(BaseModel):
    """Schema for dist store search request parameters"""

    NutroStoreID: Optional[str] = Field(default=None, max_length=50)
    DataSource: Optional[str] = Field(default=None, max_length=50)
    DistStoreID: Optional[str] = Field(default=None, max_length=50)
    DistStoreName: Optional[str] = Field(default=None, max_length=50)
    DistStoreAddress: Optional[str] = Field(default=None, max_length=50)
    DistStoreCity: Optional[str] = Field(default=None, max_length=50)
    DistStoreState: Optional[str] = Field(default=None, max_length=50)
    DistStoreZip: Optional[str] = Field(default=None, max_length=50)
    DistStorePhone: Optional[str] = Field(default=None, max_length=50)


class DistStoreResponse(BaseModel):
    """Schema for dist store search response"""

    DataSource: Optional[str]
    StoreID: Optional[str]
    StoreDesc: Optional[str]
    CityStateZip: Optional[str]
    FirstTransactionDate: Optional[str]
    LastTransactionDate: Optional[str]
    Last4POS: Optional[Decimal]
    Last52POS: Optional[Decimal]
    TotalPOSSales: Optional[Decimal]
    MappedTo: Optional[str]
    IsPending: Optional[bool]
    VCID: Optional[int] = Field(default=None, description="VCID (Mapping ID)")
    NutroStoreID: Optional[int]

    class Config:
        from_attributes = True


class DistStoreSearchResponse(BaseModel):
    """Schema for paginated dist store search response"""

    stores: List[DistStoreResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_previous: bool

    class Config:
        from_attributes = True


class UnmappedStoreSearchRequest(BaseModel):
    """Schema for unmapped store search request parameters"""

    DataSource: Optional[str] = Field(default=None, max_length=50)
    DistStoreID: Optional[str] = Field(default=None, max_length=50)
    DistStoreName: Optional[str] = Field(default=None, max_length=50)
    DistStoreAddress: Optional[str] = Field(default=None, max_length=50)
    DistStoreCity: Optional[str] = Field(default=None, max_length=50)
    DistStoreState: Optional[str] = Field(default=None, max_length=50)
    DistStoreZip: Optional[str] = Field(default=None, max_length=50)
    DistStorePhone: Optional[str] = Field(default=None, max_length=50)


class UnmappedStoreResponse(BaseModel):
    """Schema for unmapped store search response"""

    DataSource: Optional[str]
    StoreID: Optional[str]
    StoreDesc: Optional[str]
    CityStateZip: Optional[str]
    FirstDt: Optional[str]
    LastDt: Optional[str]
    Since51525: Optional[Decimal]
    Since61324: Optional[Decimal]
    Since1110: Optional[Decimal]
    Status: Optional[str]
    VCID: Optional[int] = Field(default=None, description="VCID (Mapping ID)")

    class Config:
        from_attributes = True


class UnmappedStoreSearchResponse(BaseModel):
    """Schema for paginated unmapped store search response"""

    stores: List[UnmappedStoreResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_previous: bool

    class Config:
        from_attributes = True


class StoreSuggestion(BaseModel):
    """Schema for individual store suggestion record with exact UI field names"""

    SCORE: Optional[float] = Field(default=None, description="Match score")
    STORE: Optional[str] = Field(default=None, description="Store name")
    ADDRESS: Optional[str] = Field(default=None, description="Store address")
    CITY: Optional[str] = Field(default=None, description="Store city")
    STATE: Optional[str] = Field(default=None, description="Store state")
    ZIP: Optional[str] = Field(default=None, description="Store zip code")
    PHONE: Optional[str] = Field(default=None, description="Store phone number")
    STATUS: Optional[str] = Field(default=None, description="Store status")

    class Config:
        from_attributes = True


class StoreSuggestionResponse(BaseModel):
    """Schema for store suggestion response with pagination"""

    suggestions: List[Dict[str, Any]] = Field(
        description="List of store suggestion records with exact UI field names"
    )
    total_count: int = Field(description="Total number of records returned")
    page: Optional[int] = Field(default=None, description="Current page number")
    limit: Optional[int] = Field(default=None, description="Number of records per page")
    has_next: bool = Field(default=False, description="Whether there are more pages")
    has_previous: bool = Field(
        default=False, description="Whether there are previous pages"
    )

    class Config:
        from_attributes = True


class StoreCreateRequest(BaseModel):
    """Schema for store creation request - matches stored procedure parameters"""

    # Basic store information
    StoreName: Optional[str] = Field(
        default=None, max_length=50, description="Store name"
    )
    ChainName: Optional[str] = Field(
        default=None, max_length=50, description="Chain name"
    )
    StoreNumber: Optional[str] = Field(
        default=None, max_length=10, description="Store number"
    )

    # Location and account details
    LocationType: Optional[str] = Field(
        default=None, max_length=50, description="Location type"
    )
    LocationTypeDetail: Optional[str] = Field(
        default=None, max_length=50, description="Location type detail"
    )
    AccountType: Optional[str] = Field(
        default=None, max_length=50, description="Account type"
    )
    AccountTypeDetail: Optional[str] = Field(
        default=None, max_length=50, description="Account type detail"
    )
    ReportCategory: Optional[str] = Field(
        default=None, max_length=50, description="Report category"
    )
    ReportCategoryDetail: Optional[str] = Field(
        default=None, max_length=50, description="Report category detail"
    )
    StoreStatus: Optional[str] = Field(
        default=None, max_length=10, description="Store status"
    )

    # Location address information
    LocationAddress: Optional[str] = Field(
        default=None, max_length=50, description="Location address"
    )
    LocationCity: Optional[str] = Field(
        default=None, max_length=50, description="Location city"
    )
    LocationState: Optional[str] = Field(
        default=None, max_length=50, description="Location state"
    )
    LocationZipCode: Optional[str] = Field(
        default=None, max_length=50, description="Location zip code"
    )
    LocationCountry: Optional[str] = Field(
        default=None, max_length=50, description="Location country"
    )

    # Mailing address information
    MailingAddress: Optional[str] = Field(
        default=None, max_length=50, description="Mailing address"
    )
    MailingCity: Optional[str] = Field(
        default=None, max_length=50, description="Mailing city"
    )
    MailingState: Optional[str] = Field(
        default=None, max_length=50, description="Mailing state"
    )
    MailingZipCode: Optional[str] = Field(
        default=None, max_length=50, description="Mailing zip code"
    )
    MailingCountry: Optional[str] = Field(
        default=None, max_length=50, description="Mailing country"
    )

    # Contact information
    StoreContact: Optional[str] = Field(
        default=None, max_length=50, description="Store contact person"
    )
    Phone: Optional[str] = Field(
        default=None, max_length=50, description="Phone number"
    )
    Fax: Optional[str] = Field(default=None, max_length=50, description="Fax number")
    EmailAddress: Optional[str] = Field(
        default=None, max_length=100, description="Email address"
    )
    WebAddress: Optional[str] = Field(
        default=None, max_length=100, description="Web address"
    )

    # Additional store details
    ISODate: Optional[date] = Field(default=None, description="ISO date")
    Size_Override: Optional[str] = Field(
        default=None, max_length=50, description="Size override"
    )
    BuyingGroup: Optional[str] = Field(
        default=None, max_length=50, description="Buying group"
    )
    Distribution_Notes: Optional[str] = Field(
        default=None, max_length=2000, description="Distribution notes"
    )

    # Territory and call management
    AccountTM: Optional[str] = Field(
        default=None, max_length=50, description="Account territory manager"
    )
    IsCallTarget: Optional[bool] = Field(
        default=None, description="Is call target flag"
    )
    Priority: Optional[str] = Field(
        default=None, max_length=50, description="Priority level"
    )
    CallFrequency: Optional[int] = Field(default=None, description="Call frequency")
    WhereToBuyBrands: Optional[str] = Field(
        default=None, max_length=500, description="Where to buy brands"
    )
    ProgramEnrollment: Optional[str] = Field(
        default=None, max_length=500, description="Program enrollment"
    )
    StoreNotes: Optional[str] = Field(
        default=None, max_length=2000, description="Store notes"
    )

    # Demo territory information
    DemoTerritoryDivID: Optional[int] = Field(
        default=None, description="Demo territory division ID"
    )
    DemoMarket: Optional[str] = Field(
        default=None, max_length=50, description="Demo market"
    )
    IsDemoTarget: Optional[bool] = Field(
        default=None, description="Is demo target flag"
    )
    DemoPriority: Optional[str] = Field(
        default=None, max_length=50, description="Demo priority"
    )
    DemoFrequency: Optional[int] = Field(default=None, description="Demo frequency")
    DemoNotes: Optional[str] = Field(
        default=None, max_length=2000, description="Demo notes"
    )

    # Request information
    RequestBy: Optional[str] = Field(
        default=None, max_length=50, description="Requested by"
    )
    RequestNotes: Optional[str] = Field(
        default=None, max_length=2000, description="Request notes"
    )

    # System identifiers
    VCID: Optional[int] = Field(default=None, description="VCID")
    NutroStoreID: Optional[int] = Field(
        default=None, description="Nutro Store ID (output parameter)"
    )

    class Config:
        from_attributes = True


class StoreCreateResponse(BaseModel):
    """Schema for store creation response"""

    success: bool = Field(description="Whether the store was created successfully")
    message: str = Field(description="Success or error message")
    

    class Config:
        from_attributes = True


class MappingUnmappingMetadataRequest(BaseModel):
    VCID: int
    NutrostoreID: Optional[int]


class MappingUnmappingUpdateMetadataRequest(BaseModel):
    """Schema for updating store mapping metadata fields"""

    VCID: int = Field(description="VCID (Mapping ID) to identify the record to update")
    customer_type: Optional[str] = Field(default=None, description="Customer type")
    rep: Optional[str] = Field(default=None, description="Representative")
    address: Optional[str] = Field(default=None, description="Store address")
    warehouse: Optional[str] = Field(default=None, description="Warehouse information")
    country: Optional[str] = Field(default=None, description="Store country")
    phone: Optional[str] = Field(default=None, description="Store phone number")

    class Config:
        from_attributes = True


class MappingUnmappingUpdateMetadataResponse(BaseModel):
    """Schema for store mapping metadata update response"""

    success: bool = Field(description="Whether the update was successful")
    message: str = Field(description="Success or error message")
    updated_fields: List[str] = Field(description="List of fields that were updated")
    VCID: int = Field(description="VCID of the updated record")

    class Config:
        from_attributes = True


class StoreBase(BaseModel):
    NutroStoreID: Optional[int] = None
    StoreName: Optional[str] = None
    LocationAddress: Optional[str] = None
    LocationCity: Optional[str] = None
    LocationState: Optional[str] = None
    LocationZipCode: Optional[str] = None
    LocationCountry: Optional[str] = None
    Phone: Optional[str] = None
    ChainName: Optional[str] = None
    StoreNumber: Optional[str] = None
    StoreStatus: Optional[str] = None
    AccountType: Optional[str] = None
    ReportCategory: Optional[str] = None
    ReportCategoryDetail: Optional[str] = None
    Priority: Optional[str] = None
    TerritoryManager: Optional[str] = None
    DistrictManager: Optional[str] = None
    KAM: Optional[str] = None


class StoreAttributionResponse(StoreBase):
    class Config:
        from_attributes = True


class StoreListResponse(BaseModel):
    stores: List[StoreAttributionResponse]
    total_count: int
    page: int
    limit: int
    has_next: bool
    has_previous: bool


class StoreListTotalResponse(BaseModel):
    stores: List[StoreAttributionResponse]
    total_count: int


class StoreUpdateRequest(BaseModel):
    StoreName: Optional[str] = None
    LocationAddress: Optional[str] = None
    LocationCity: Optional[str] = None
    LocationState: Optional[str] = None
    LocationZipCode: Optional[str] = None
    LocationCountry: Optional[str] = None
    Phone: Optional[str] = None
    ChainName: Optional[str] = None
    StoreNumber: Optional[str] = None
    StoreStatus: Optional[str] = None
    AccountType: Optional[str] = None
    ReportCategory: Optional[str] = None
    ReportCategoryDetail: Optional[str] = None
    Priority: Optional[str] = None
    TerritoryManager: Optional[str] = None
    DistrictManager: Optional[str] = None
    KAM: Optional[str] = None


class DeleteRequest(BaseModel):
    nutro_store_ids: List[int]


class StoreFilterValuesResponse(BaseModel):
    field: str
    values: List[Any]
    total: int
    limit: int
    offset: int

    class Config:
        schema_extra = {
            "example": {
                "field": "store_names",
                "values": ["Store A", "Store B", "Store C"],
                "total": 150,
                "limit": 3,
                "offset": 0,
            }
        }


class FindStoreResult(BaseModel):
    """Schema for individual store search result"""

    NutroStoreID: Optional[str] = Field(default=None, description="Nutro Store ID")
    ChainName: Optional[str] = Field(default=None, description="Chain name")
    StoreName: Optional[str] = Field(default=None, description="Store name")
    StoreNumber: Optional[str] = Field(default=None, description="Store number")
    Address: Optional[str] = Field(default=None, description="Store address")
    City: Optional[str] = Field(default=None, description="Store city")
    State: Optional[str] = Field(default=None, description="Store state")
    ZipCode: Optional[str] = Field(default=None, description="Store zip code")
    Phone: Optional[str] = Field(default=None, description="Store phone number")

    class Config:
        from_attributes = True


class FindStoreResponse(BaseModel):
    """Schema for store search response with pagination"""

    results: List[Dict[str, Any]] = Field(description="List of store search results")
    total_count: int = Field(description="Total number of records returned")
    page: Optional[int] = Field(default=None, description="Current page number")
    limit: Optional[int] = Field(default=None, description="Number of records per page")
    has_next: bool = Field(default=False, description="Whether there are more pages")
    has_previous: bool = Field(
        default=False, description="Whether there are previous pages"
    )

    class Config:
        from_attributes = True


class FindSearchParams(BaseModel):
    """Schema for store search request parameters"""

    nutro_store_id: Optional[str] = Field(
        default=None,
        description="Nutro Store ID to search for",
        example="150088",
        alias="NutroStoreID",
    )
    chain_name: Optional[str] = Field(
        default=None,
        description="Chain name to search for",
        example="Walmart",
        alias="ChainName",
    )
    store_name: Optional[str] = Field(
        default=None,
        description="Store name to search for",
        example="Super Center",
        alias="StoreName",
    )
    store_number: Optional[str] = Field(
        default=None,
        description="Store number to search for",
        example="1234",
        alias="StoreNumber",
    )
    address: Optional[str] = Field(
        default=None,
        description="Address to search for",
        example="123 Main St",
        alias="Address",
    )
    city: Optional[str] = Field(
        default=None, description="City to search for", example="New York", alias="City"
    )
    state: Optional[str] = Field(
        default=None, description="State to search for", example="NY", alias="State"
    )
    zip_code: Optional[str] = Field(
        default=None,
        description="Zip code to search for",
        example="10001",
        alias="ZipCode",
    )
    phone: Optional[str] = Field(
        default=None,
        description="Phone number to search for",
        example="************",
        alias="Phone",
    )

    class Config:
        allow_population_by_field_name = True
        json_encoders = {
            # Add any custom JSON encoders if needed
        }
