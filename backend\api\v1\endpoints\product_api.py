from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Union

from services.product_service import ProductService
from api.v1.deps import get_db
from api.v1.wrappers import retry_request
from schema.product_schema import (
    ProductSearchResponse,
    ProductExtensionResponseList,
    ProductMappingResponseList,
    ProductResponse,
    ListItemResponse,
    PriceListResponse,
    CustomerNameResponse,
    ProductExtensionResponse,
    ProductDetailsResponse,
    ProductMappingResponse,
    ProductBasicResponse,
)

router = APIRouter()

product_service = ProductService()


# Update product_api.py


@router.get("/search", response_model=ProductSearchResponse)
@retry_request(max_retries=5, delay=5)
def search_products(
    BrandName: Optional[str] = Query(
        default=None, max_length=50, description="Brand Name"
    ),
    SkuName: Optional[str] = Query(default=None, max_length=50, description="SKU Name"),
    PRMSDescription: Optional[str] = Query(
        default=None, max_length=50, description="PRMS Description"
    ),
    RetailUPC: Optional[str] = Query(
        default=None, max_length=50, description="Retail UPC"
    ),
    PRDNO: Optional[str] = Query(
        default=None, max_length=50, description="Product Number"
    ),
    AssociatedItem: Optional[str] = Query(
        default=None, max_length=100, description="Associated Item"
    ),
    page: int = Query(default=1, ge=1, description="Page number (1-based)"),
    limit: int = Query(
        default=100, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """
    Search products using the product_Search stored procedure with pagination

    Returns paginated products with these required fields:
    - PRDNO (Product number)
    - DESCP (Description)
    - ItemID
    - SkuName
    - AssociatedItem

    All other fields are optional.
    """
    try:
        result = product_service.search_products(
            db,
            BrandName=BrandName,
            SkuName=SkuName,
            PRMSDescription=PRMSDescription,
            RetailUPC=RetailUPC,
            PRDNO=PRDNO,
            AssociatedItem=AssociatedItem,
            page=page,
            limit=limit,
        )

        # Validate that all products have the required fields
        for product in result.products:
            if not all(
                [
                    product.PRDNO,
                    product.DESCP,
                    product.ItemID,
                    product.SkuName,
                    product.AssociatedItem,
                ]
            ):
                raise HTTPException(
                    status_code=500,
                    detail="Required product fields missing from database response",
                )

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Unexpected error during product search: {str(e)}"
        )


@router.get("/mappings/{prdno}", response_model=ProductMappingResponseList)
@retry_request(max_retries=5, delay=5)
def get_product_mappings(
    prdno: str,
    page: int = Query(default=1, ge=1, description="Page number (1-based)"),
    limit: int = Query(
        default=100, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """Get product mappings for a specific product with pagination"""
    return product_service.get_product_mappings(db, prdno, page=page, limit=limit)


@router.get("/extensions/{prdno}", response_model=ProductExtensionResponseList)
@retry_request(max_retries=5, delay=5)
def get_product_extensions(
    prdno: str,
    page: int = Query(default=1, ge=1, description="Page number (1-based)"),
    limit: int = Query(
        default=100, ge=1, le=1000, description="Number of records per page"
    ),
    db: Session = Depends(get_db),
):
    """Get product extensions for a specific product with pagination"""
    return product_service.get_product_extensions(db, prdno, page=page, limit=limit)


@router.get("/list-items", response_model=Union[List[str], List[ListItemResponse]])
@retry_request(max_retries=5, delay=5)
def get_list_items(
    list_name: Optional[str] = Query(None, description="Filter by specific list name"),
    parent_list_name: Optional[str] = Query(
        None, description="Parent list name filter"
    ),
    parent_item_value: Optional[str] = Query(
        None, description="Parent item value filter"
    ),
    db: Session = Depends(get_db),
):
    """
    Get list items - returns either:
    - Distinct list names (when no parameters provided)
    - Full list items (when parameters provided)
    """
    try:
        if not any([list_name, parent_list_name, parent_item_value]):
            # Return just distinct list names
            return product_service.get_distinct_list_names(db)
        else:
            # Return full list items with filters
            return product_service.get_list_items(
                db,
                ListName=list_name,
                ParentListName=parent_list_name,
                ParentItemValue=parent_item_value,
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving list items: {str(e)}"
        )


@router.get("/price-lists/{prdno}", response_model=List[str])
@retry_request(max_retries=5, delay=5)
def get_price_lists(prdno: str, db: Session = Depends(get_db)):
    """Get price lists for a specific product"""
    return product_service.get_price_lists(db, prdno)


@router.get("/customer-names/{prdno}", response_model=List[CustomerNameResponse])
@retry_request(max_retries=5, delay=5)
def get_customer_names(prdno: str, db: Session = Depends(get_db)):
    """Get customer names associated with a product"""
    return product_service.get_customer_names(db, prdno)


@router.get("/details/{prdno}", response_model=ProductDetailsResponse)
@retry_request(max_retries=5, delay=5)
def get_product_details(prdno: str, db: Session = Depends(get_db)):
    """Get detailed information for a specific product"""
    return product_service.get_product_details(db, prdno)


@router.get("/basic/{prdno}", response_model=ProductBasicResponse)
@retry_request(max_retries=5, delay=5)
def get_product_basic(prdno: str, db: Session = Depends(get_db)):
    """Get basic product information"""
    return product_service.get_product_basic(db, prdno)
